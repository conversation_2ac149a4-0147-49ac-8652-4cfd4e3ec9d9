<?php
/**
 * Menu Manager
 *
 * Gestione centralizzata di tutti i menu di WordPress per Financial Advisor
 *
 * @package Financial_Advisor
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Financial_Advisor_Menu_Manager {
    /**
     * Istanza singleton
     * @var Financial_Advisor_Menu_Manager
     */
    private static $instance = null;

    /**
     * Slug principale del menu
     * @var string
     */
    public $main_menu_slug = 'document-viewer-settings';

    /**
     * Constructor
     */
    private function __construct() {
        // Rimuovi i vecchi hook di menu prima di aggiungerne di nuovi
        remove_action('admin_menu', array('Document_Viewer_Settings', 'admin_menu'));
        remove_action('admin_menu', array('Financial_Academy_Manager', 'add_admin_menu'));

        // Aggiungi l'azione per il menu
        add_action('admin_menu', array($this, 'register_all_menus'));

        // Gestisci i redirect
        add_action('init', array($this, 'handle_menu_redirects'));
    }

    /**
     * Ottieni l'istanza singleton della classe
     *
     * @return Financial_Advisor_Menu_Manager
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * Registra tutti i menu e sottomenu del plugin
     */
    public function register_all_menus() {
        // 1. Menu principale Financial Advisor
        add_menu_page(
            __('Financial Advisor Settings', 'document-viewer-plugin'),
            __('Financial Advisor', 'document-viewer-plugin'),
            'manage_options',
            $this->main_menu_slug,
            array($this, 'render_settings_page'),
            'dashicons-media-document',
            10
        );

        // 2. Sottomenu Impostazioni (punta alla stessa pagina del menu principale)
        add_submenu_page(
            $this->main_menu_slug,
            __('Financial Advisor Settings', 'document-viewer-plugin'),
            __('Settings', 'document-viewer-plugin'),
            'manage_options',
            $this->main_menu_slug,
            array($this, 'render_settings_page')
        );

        // 3. Sottomenu Financial Academy
        add_submenu_page(
            $this->main_menu_slug,
            __('Financial Academy Questions', 'document-viewer-plugin'),
            __('Financial Academy', 'document-viewer-plugin'),
            'manage_options',
            'financial-academy-manager',
            array($this, 'render_academy_page')
        );

        // 4. Sottomenu Office Add-in
        add_submenu_page(
            $this->main_menu_slug,
            __('Office Add-in', 'document-viewer-plugin'),
            __('Office Add-in', 'document-viewer-plugin'),
            'manage_options',
            'office-addin-manager',
            array($this, 'render_office_addin_page')
        );

        // 5. Aggiungi qui altri sottomenu se necessario...
    }

    /**
     * Gestisce i reindirizzamenti per gli URL diretti
     */
    public function handle_menu_redirects() {
        // Verifica se l'URL corrente è /wp-admin/financial-academy-manager (accesso diretto)
        $current_url = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';

        // Solo se l'URL è diretto, reindirizza alla pagina appropriata
        // Per ora non fa niente, poiché vogliamo che /wp-admin/financial-academy-manager funzioni normalmente

        // Il problema precedente era causato dal tentativo di reindirizzare tutti gli accessi a financial-academy-manager
        // Ora gestiamo correttamente i sottomenu attraverso la registrazione centralizzata
    }

    /**
     * Renderizza la pagina delle impostazioni
     *
     * Delega la visualizzazione alla classe Document_Viewer_Settings
     */
    public function render_settings_page() {
        // Usa il sistema di controllo accessi FA per verificare l'accesso admin
        if (!apply_filters('fa_user_can_access', false, 'admin')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        // Se esiste la classe Document_Viewer_Settings e ha il metodo settings_page
        if (class_exists('Document_Viewer_Settings')) {
            $settings = new Document_Viewer_Settings();
            if (method_exists($settings, 'settings_page')) {
                $settings->settings_page();
                return;
            }
        }

        // Fallback se la classe Document_Viewer_Settings non è disponibile
        echo '<div class="wrap">';
        echo '<h1>' . __('Financial Advisor Settings', 'document-viewer-plugin') . '</h1>';
        echo '<p>' . __('Settings page content not available.', 'document-viewer-plugin') . '</p>';
        echo '</div>';
    }

    /**
     * Renderizza la pagina Financial Academy
     *
     * Delega la visualizzazione alla classe Financial_Academy_Manager
     */
    public function render_academy_page() {
        // Usa il sistema di controllo accessi FA per verificare l'accesso admin
        if (!apply_filters('fa_user_can_access', false, 'admin')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }

        // Se esiste la classe Financial_Academy_Manager e ha il metodo render_admin_page
        if (class_exists('Financial_Academy_Manager')) {
            $academy_manager = financial_academy_manager();
            if (method_exists($academy_manager, 'render_admin_page')) {
                $academy_manager->render_admin_page();
                return;
            }
        }

        // Fallback se la classe Financial_Academy_Manager non è disponibile
        echo '<div class="wrap">';
        echo '<h1>' . __('Financial Academy', 'document-viewer-plugin') . '</h1>';
        echo '<p>' . __('Financial Academy page content not available.', 'document-viewer-plugin') . '</p>';
        echo '</div>';
    }

    /**
     * Renderizza la pagina Office Add-in
     *
     * Gestisce la visualizzazione e la configurazione dell'Office Add-in
     */
    public function render_office_addin_page() {
        // Usa il sistema di controllo accessi FA per verificare l'accesso admin
        if (!apply_filters('fa_user_can_access', false, 'admin')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'document-viewer-plugin'));
        }

        // Carica gli script e gli stili necessari per la pagina
        wp_enqueue_script('office-addin-preview-script');
        wp_enqueue_style('office-addin-preview-style');

        // Localizza lo script con i dati necessari per AJAX
        wp_localize_script(
            'office-addin-preview-script',
            'office_addin_preview_params',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('document_viewer_nonce')
            )
        );

        // Carica gli script e gli stili per l'editor avanzato
        wp_enqueue_editor();
        wp_enqueue_media();

        // Carica gli script e gli stili per il color picker
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');

        // Carica gli script e gli stili per l'editor avanzato dell'Office Add-in
        wp_enqueue_script('office-addin-editor-script');
        wp_enqueue_style('office-addin-editor-style');

        // Salva le impostazioni se il form è stato inviato
        if (isset($_POST['office_addin_content']) && check_admin_referer('office_addin_settings', 'office_addin_nonce')) {
            // Salva il contenuto HTML dell'add-in senza filtrare il contenuto
            // Utilizziamo stripslashes per rimuovere eventuali caratteri di escape aggiunti automaticamente
            $content = stripslashes($_POST['office_addin_content']);
            update_option('office_addin_content', $content);

            // Salva il CSS dell'add-in
            if (isset($_POST['office_addin_css'])) {
                $css = stripslashes($_POST['office_addin_css']);
                update_option('office_addin_css', $css);
            }

            // Salva l'URL dell'add-in
            if (isset($_POST['office_addin_url'])) {
                update_option('office_addin_url', esc_url_raw($_POST['office_addin_url']));
            }

            echo '<div class="notice notice-success is-dismissible"><p>' . __('Office Add-in settings saved.', 'document-viewer-plugin') . '</p></div>';
        }

        // Ottieni il contenuto HTML dell'add-in
        $office_addin_content = get_option('office_addin_content');

        // Se il contenuto è vuoto, carica il contenuto predefinito
        if (empty($office_addin_content)) {
            // Prova a ottenere il contenuto predefinito dalla classe Document_Viewer_Settings
            if (class_exists('Document_Viewer_Settings')) {
                $settings = new Document_Viewer_Settings();
                if (method_exists($settings, 'get_default_office_addin_content')) {
                    try {
                        $office_addin_content = $settings->get_default_office_addin_content();
                    } catch (Exception $e) {
                        // In caso di errore, usa un contenuto HTML di base
                        $office_addin_content = $this->get_fallback_office_addin_content();
                    }
                } else {
                    // Se il metodo non esiste, usa un contenuto HTML di base
                    $office_addin_content = $this->get_fallback_office_addin_content();
                }
            } else {
                // Se la classe non esiste, usa un contenuto HTML di base
                $office_addin_content = $this->get_fallback_office_addin_content();
            }
        }

        // Renderizza la pagina
        ?>
        <div class="wrap">
            <h1><?php _e('Office Add-in Manager', 'document-viewer-plugin'); ?></h1>

            <div class="office-addin-description">
                <p><?php _e('Configure the HTML content for the Excel add-in interface. The add-in will automatically use the API settings from the Primary API tab.', 'document-viewer-plugin'); ?></p>
            </div>

            <div class="office-addin-tabs">
                <div class="nav-tab-wrapper">
                    <a href="#content-tab" class="nav-tab nav-tab-active"><?php _e('Content', 'document-viewer-plugin'); ?></a>
                    <a href="#preview-tab" class="nav-tab"><?php _e('Live Preview', 'document-viewer-plugin'); ?></a>
                    <a href="#manifest-tab" class="nav-tab"><?php _e('Manifest', 'document-viewer-plugin'); ?></a>
                    <a href="#documentation-tab" class="nav-tab"><?php _e('Documentation', 'document-viewer-plugin'); ?></a>
                </div>

                <div id="content-tab" class="tab-content active">
                    <form method="post" action="" id="office-addin-form">
                        <?php wp_nonce_field('office_addin_settings', 'office_addin_nonce'); ?>

                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php _e('Add-in URL', 'document-viewer-plugin'); ?></th>
                                <td>
                                    <?php
                                    $addin_url = get_option('office_addin_url', trailingslashit(get_site_url()) . 'office-addin/');
                                    ?>
                                    <input type="url" name="office_addin_url" id="office_addin_url" value="<?php echo esc_url($addin_url); ?>" class="regular-text">
                                    <p class="description"><?php _e('The URL where the Excel add-in will be accessible. This should be a publicly accessible URL.', 'document-viewer-plugin'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php _e('Add-in HTML Content', 'document-viewer-plugin'); ?></th>
                                <td>
                                    <?php
                                    // Configurazione dell'editor avanzato
                                    $editor_settings = array(
                                        'media_buttons' => true, // Mostra il pulsante per caricare media
                                        'textarea_name' => 'office_addin_content',
                                        'textarea_rows' => 20,
                                        'teeny'         => false, // Usa la versione completa dell'editor
                                        'tinymce'       => array(
                                            'plugins'                      => 'textcolor,colorpicker,hr,lists,media,paste,tabfocus,wplink,wordpress,wpeditimage,wpgallery,wpview',
                                            'toolbar1'                     => 'formatselect,bold,italic,underline,strikethrough,forecolor,backcolor,|,bullist,numlist,|,blockquote,hr,|,alignleft,aligncenter,alignright,|,link,unlink,|,wp_adv',
                                            'toolbar2'                     => 'pastetext,removeformat,charmap,outdent,indent,|,undo,redo,|,wp_help',
                                            'content_css'                  => plugin_dir_url(dirname(__FILE__)) . 'assets/css/office-addin-editor.css'
                                        ),
                                        'quicktags'     => array(
                                            'buttons' => 'strong,em,link,block,del,ins,img,ul,ol,li,code,more,close'
                                        ),
                                        'editor_css'    => '<style>.wp-editor-area { height: 400px !important; }</style>'
                                    );

                                    // Usa l'editor avanzato di WordPress
                                    if (function_exists('wp_editor')) {
                                        wp_editor($office_addin_content, 'office_addin_content_editor', $editor_settings);
                                    } else {
                                        // Fallback a un semplice textarea se wp_editor non è disponibile
                                        ?>
                                        <textarea name="office_addin_content" id="office_addin_content" rows="20" cols="50" class="large-text"><?php echo esc_textarea($office_addin_content); ?></textarea>
                                        <?php
                                    }
                                    ?>

                                    <div class="addin-editor-tools">
                                        <h4><?php _e('Editor Tools', 'document-viewer-plugin'); ?></h4>
                                        <div class="addin-editor-color-tools">
                                            <label for="addin-custom-color"><?php _e('Custom Color:', 'document-viewer-plugin'); ?></label>
                                            <input type="text" id="addin-custom-color" class="color-picker" data-default-color="#3498db" />
                                            <button type="button" id="apply-text-color" class="button"><?php _e('Apply to Text', 'document-viewer-plugin'); ?></button>
                                            <button type="button" id="apply-bg-color" class="button"><?php _e('Apply to Background', 'document-viewer-plugin'); ?></button>
                                        </div>

                                        <div class="addin-editor-template-tools">
                                            <label for="addin-insert-template"><?php _e('Insert Template Element:', 'document-viewer-plugin'); ?></label>
                                            <select id="addin-insert-template" class="regular-text">
                                                <option value=""><?php _e('-- Select Element --', 'document-viewer-plugin'); ?></option>
                                                <option value="button"><?php _e('Button', 'document-viewer-plugin'); ?></option>
                                                <option value="section"><?php _e('Section', 'document-viewer-plugin'); ?></option>
                                                <option value="form-field"><?php _e('Form Field', 'document-viewer-plugin'); ?></option>
                                                <option value="result-container"><?php _e('Result Container', 'document-viewer-plugin'); ?></option>
                                            </select>
                                            <button type="button" id="insert-template-element" class="button"><?php _e('Insert', 'document-viewer-plugin'); ?></button>
                                        </div>
                                    </div>

                                    <p class="description">
                                        <?php _e('This HTML content will be used for the Excel add-in interface. Use the advanced editor to format text, add colors, and insert media.', 'document-viewer-plugin'); ?>
                                    </p>
                                </td>
                            </tr>
                            <?php
                            // Ottieni il CSS dell'add-in (nascosto all'utente ma salvato nel database)
                            $office_addin_css = get_option('office_addin_css', '');
                            if (empty($office_addin_css)) {
                                // Se non esiste, carica il CSS predefinito
                                if (class_exists('Document_Viewer_Settings')) {
                                    $settings = new Document_Viewer_Settings();
                                    if (method_exists($settings, 'get_default_office_addin_css')) {
                                        $office_addin_css = $settings->get_default_office_addin_css();
                                    } else {
                                        // CSS standard per Excel add-in
                                        $office_addin_css = '/* CSS standard per Excel add-in - Non modificare */
/* Questi stili sono necessari per il corretto funzionamento dell\'add-in */
.excel-addin-container {
    font-family: "Segoe UI", Arial, sans-serif;
    padding: 10px;
    width: 320px;
    margin: 0 auto;
    box-sizing: border-box;
}
.section {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f9f9f9;
}
h2 {
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    font-size: 18px;
}
h3 {
    color: #3498db;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
}
.form-group {
    margin-bottom: 10px;
}
label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}
.primary-button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}
.secondary-button {
    background-color: #95a5a6;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
}';
                                    }
                                } else {
                                    // CSS standard per Excel add-in
                                    $office_addin_css = '/* CSS standard per Excel add-in - Non modificare */
/* Questi stili sono necessari per il corretto funzionamento dell\'add-in */
.excel-addin-container {
    font-family: "Segoe UI", Arial, sans-serif;
    padding: 10px;
    width: 320px;
    margin: 0 auto;
    box-sizing: border-box;
}
.section {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f9f9f9;
}
h2 {
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    font-size: 18px;
}
h3 {
    color: #3498db;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
}
.form-group {
    margin-bottom: 10px;
}
label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}
.primary-button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}
.secondary-button {
    background-color: #95a5a6;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
}';
                                }
                            }
                            // Salva il CSS in un campo nascosto per mantenerlo disponibile per l'anteprima
                            ?>
                            <input type="hidden" name="office_addin_css" id="office_addin_css" value="<?php echo esc_attr($office_addin_css); ?>" />
                        </table>

                        <p class="submit">
                            <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php _e('Save Changes', 'document-viewer-plugin'); ?>">
                            <button type="button" id="preview-changes" class="button button-secondary"><?php _e('Preview Changes', 'document-viewer-plugin'); ?></button>
                        </p>
                    </form>
                </div>

                <div id="preview-tab" class="tab-content">
                    <h2><?php _e('Live Preview', 'document-viewer-plugin'); ?></h2>
                    <p><?php _e('This is a live preview of how your Excel add-in will look and function. You can test the UI components here before saving.', 'document-viewer-plugin'); ?></p>

                    <!-- Excel-like Grid Section (Outside the Add-in Panel) -->
                    <div class="excel-grid-external-container">
                        <h3><?php _e('Excel Data Simulation Grid', 'document-viewer-plugin'); ?></h3>
                        <p><?php _e('Use this grid to simulate Excel data. Select cells and test the extraction functionality.', 'document-viewer-plugin'); ?></p>
                        <div id="external-excel-grid-container">
                            <!-- Grid will be inserted here by JavaScript -->
                        </div>
                        <div class="grid-actions">
                            <button type="button" id="test-extraction" class="button button-primary">
                                <?php _e('Test Text Extraction', 'document-viewer-plugin'); ?>
                            </button>
                            <button type="button" id="test-analysis" class="button button-secondary">
                                <?php _e('Test Analysis with Query', 'document-viewer-plugin'); ?>
                            </button>
                            <div id="extraction-results" class="extraction-results" style="display: none;">
                                <h4><?php _e('Extracted Text:', 'document-viewer-plugin'); ?></h4>
                                <div id="extracted-text-display" class="extracted-text-display"></div>
                            </div>
                        </div>
                    </div>

                    <div class="addin-preview-container">
                        <div class="addin-preview-header">
                            <span class="addin-preview-title"><?php _e('Excel Add-in Preview', 'document-viewer-plugin'); ?></span>
                            <div class="addin-preview-controls">
                                <button type="button" id="refresh-preview" class="button button-small" title="<?php _e('Refresh Preview', 'document-viewer-plugin'); ?>"><span class="dashicons dashicons-update"></span></button>
                                <button type="button" id="toggle-preview-size" class="button button-small" title="<?php _e('Toggle Size', 'document-viewer-plugin'); ?>"><span class="dashicons dashicons-editor-expand"></span></button>
                            </div>
                        </div>
                        <div class="addin-preview-frame-container">
                            <div class="addin-preview-loading" id="addin-preview-loading">
                                <div class="spinner is-active"></div>
                                <p>Loading preview...</p>
                            </div>
                            <iframe id="addin-preview-frame" src="about:blank" frameborder="0"></iframe>
                        </div>
                    </div>

                    <div class="addin-preview-info">
                        <h3><?php _e('Testing Notes', 'document-viewer-plugin'); ?></h3>
                        <ul>
                            <li><?php _e('This preview simulates the Excel add-in environment.', 'document-viewer-plugin'); ?></li>
                            <li><?php _e('The "Extract Selected Cells" button will show sample text since Excel is not available in this preview.', 'document-viewer-plugin'); ?></li>
                            <li><?php _e('API connections will use your actual WordPress settings.', 'document-viewer-plugin'); ?></li>
                            <li><?php _e('Changes made in the Content tab will be reflected here after clicking "Preview Changes".', 'document-viewer-plugin'); ?></li>
                        </ul>
                    </div>

                    <!-- Nota: La funzionalità di anteprima è gestita dal file office-addin-preview.js -->
                    <script>
                    jQuery(document).ready(function($) {
                        console.log('Preview tab initialization script loaded');

                        // Carica lo script office-addin-preview.js dinamicamente se non è già caricato
                        function loadPreviewScript() {
                            if (typeof window.updatePreview === 'function') {
                                console.log('updatePreview function already available');
                                return Promise.resolve();
                            }

                            return new Promise(function(resolve, reject) {
                                console.log('Loading preview script dynamically...');
                                var script = document.createElement('script');
                                script.type = 'text/javascript';
                                script.src = '<?php echo esc_url(plugins_url('assets/js/office-addin-preview.js', dirname(__FILE__))); ?>?ver=' + new Date().getTime();
                                script.onload = function() {
                                    console.log('Preview script loaded successfully');
                                    resolve();
                                };
                                script.onerror = function() {
                                    console.error('Failed to load preview script');
                                    reject(new Error('Failed to load preview script'));
                                };
                                document.head.appendChild(script);
                            });
                        }

                        // Mostra un messaggio di errore nell'iframe
                        function showErrorMessage(message) {
                            var previewFrame = document.getElementById('addin-preview-frame');
                            if (previewFrame) {
                                try {
                                    // Create error HTML content
                                    var errorHtml = `
                                        <!DOCTYPE html>
                                        <html>
                                        <head>
                                            <meta charset="utf-8">
                                            <style>
                                                body { font-family: 'Segoe UI', Arial, sans-serif; padding: 20px; }
                                                .error { color: #e74c3c; }
                                                .container { max-width: 320px; margin: 0 auto; text-align: center; }
                                                button { background-color: #3498db; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; }
                                                pre { background: #f8f8f8; padding: 10px; border-radius: 4px; text-align: left; overflow: auto; }
                                            </style>
                                        </head>
                                        <body>
                                            <div class="container">
                                                <h2>Excel Add-in Preview</h2>
                                                <p class="error">Impossibile caricare l'anteprima.</p>
                                                <pre>${message || 'Lo script di anteprima non è stato caricato correttamente.'}</pre>
                                                <p>Prova a:</p>
                                                <ol style="text-align: left; display: inline-block;">
                                                    <li>Ricaricare la pagina</li>
                                                    <li>Svuotare la cache del browser</li>
                                                    <li>Verificare la console per errori</li>
                                                </ol>
                                                <p><button onclick="location.reload()">Ricarica la pagina</button></p>
                                            </div>
                                        </body>
                                        </html>
                                    `;

                                    // Use blob URL method instead of document.write to avoid browser warnings
                                    var blob = new Blob([errorHtml], { type: 'text/html' });
                                    var blobURL = URL.createObjectURL(blob);

                                    previewFrame.onload = function() {
                                        // Clean up the blob URL after loading
                                        URL.revokeObjectURL(blobURL);
                                    };

                                    previewFrame.src = blobURL;
                                } catch (error) {
                                    console.error('Error showing error message:', error);
                                }
                            }
                        }

                        // Inizializza l'anteprima
                        function initializePreview() {
                            console.log('Initializing preview...');

                            // Verifica che l'iframe esista
                            var previewFrame = document.getElementById('addin-preview-frame');
                            if (!previewFrame) {
                                console.error('Preview frame not found');
                                return;
                            }

                            // Verifica che la funzione updatePreview sia disponibile
                            if (typeof window.updatePreview !== 'function') {
                                console.error('updatePreview function not available');
                                showErrorMessage('La funzione updatePreview non è disponibile. Controlla la console per errori.');
                                return;
                            }

                            // Aggiorna l'anteprima
                            console.log('Calling updatePreview...');
                            try {
                                window.updatePreview();
                            } catch (error) {
                                console.error('Error calling updatePreview:', error);
                                showErrorMessage('Errore durante l\'aggiornamento dell\'anteprima: ' + error.message);
                            }
                        }

                        // Carica lo script e inizializza l'anteprima
                        loadPreviewScript()
                            .then(function() {
                                // Attendi un po' per assicurarti che lo script sia completamente inizializzato
                                setTimeout(function() {
                                    if ($('#preview-tab').hasClass('active') || $('#preview-tab').is(':visible')) {
                                        initializePreview();
                                        // Initialize external grid
                                        if (typeof window.initializeExternalGrid === 'function') {
                                            window.initializeExternalGrid();
                                        }
                                    }

                                    // We no longer need to bind tab events here since we have a centralized handler
                                    // The main tab event handler is defined in the jQuery document ready function below
                                }, 300);
                            })
                            .catch(function(error) {
                                console.error('Failed to initialize preview:', error);
                                showErrorMessage('Impossibile inizializzare l\'anteprima: ' + error.message);
                            });
                    });
                    </script>
                </div>

                <div id="manifest-tab" class="tab-content">
                    <h2><?php _e('Add-in Manifest', 'document-viewer-plugin'); ?></h2>
                    <p><?php _e('Download the manifest file to install the Excel add-in.', 'document-viewer-plugin'); ?></p>

                    <p>
                        <a href="<?php echo esc_url(admin_url('admin-ajax.php?action=download_office_addin_manifest')); ?>" class="button button-primary">
                            <?php _e('Download Manifest', 'document-viewer-plugin'); ?>
                        </a>
                    </p>

                    <div class="manifest-instructions">
                        <h3><?php _e('Installation Instructions', 'document-viewer-plugin'); ?></h3>
                        <ol>
                            <li><?php _e('Download the manifest file using the button above.', 'document-viewer-plugin'); ?></li>
                            <li><?php _e('Open Excel and go to the Insert tab.', 'document-viewer-plugin'); ?></li>
                            <li><?php _e('Click on "Get Add-ins" or "Office Add-ins".', 'document-viewer-plugin'); ?></li>
                            <li><?php _e('Select "Upload My Add-in" and browse to the downloaded manifest file.', 'document-viewer-plugin'); ?></li>
                            <li><?php _e('Click "Install" to complete the installation.', 'document-viewer-plugin'); ?></li>
                        </ol>
                    </div>
                </div>

                <div id="documentation-tab" class="tab-content">
                    <h2><?php _e('Documentation', 'document-viewer-plugin'); ?></h2>

                    <?php
                    // Carica la documentazione dal file Markdown
                    $documentation_file = plugin_dir_path(dirname(__FILE__)) . 'office-addin-documentation.md';
                    if (file_exists($documentation_file)) {
                        $documentation = file_get_contents($documentation_file);

                        // Converti Markdown in HTML (semplice)
                        $documentation = nl2br(esc_html($documentation));
                        $documentation = preg_replace('/^# (.*?)$/m', '<h2>$1</h2>', $documentation);
                        $documentation = preg_replace('/^## (.*?)$/m', '<h3>$1</h3>', $documentation);
                        $documentation = preg_replace('/^### (.*?)$/m', '<h4>$1</h4>', $documentation);
                        $documentation = preg_replace('/\*\*(.*?)\*\*/m', '<strong>$1</strong>', $documentation);
                        $documentation = preg_replace('/\*(.*?)\*/m', '<em>$1</em>', $documentation);

                        echo '<div class="office-addin-documentation">';
                        echo $documentation;
                        echo '</div>';
                    } else {
                        echo '<p>' . __('Documentation not available.', 'document-viewer-plugin') . '</p>';
                    }
                    ?>
                </div>
            </div>

            <script>
                jQuery(document).ready(function($) {
                    // Tab navigation
                    $('.office-addin-tabs .nav-tab').off('click').on('click', function(e) {
                        e.preventDefault();

                        // Hide all tab content
                        $('.tab-content').removeClass('active');

                        // Remove active class from all tabs
                        $('.nav-tab').removeClass('nav-tab-active');

                        // Add active class to clicked tab
                        $(this).addClass('nav-tab-active');

                        // Show the corresponding tab content
                        var targetTab = $(this).attr('href');
                        $(targetTab).addClass('active');

                        // If we're on the preview tab, ensure preview is initialized
                        if (targetTab === '#preview-tab') {
                            setTimeout(function() {
                                if (typeof initializePreview === 'function') {
                                    initializePreview();
                                } else if (typeof window.updatePreview === 'function') {
                                    window.updatePreview();
                                }

                                // Initialize external grid
                                if (typeof window.initializeExternalGrid === 'function') {
                                    window.initializeExternalGrid();
                                }
                            }, 300);
                        }
                    });

                    // Ensure tab navigation works on initial page load
                    var hash = window.location.hash;
                    if (hash && $('.office-addin-tabs .nav-tab[href="' + hash + '"]').length) {
                        $('.office-addin-tabs .nav-tab[href="' + hash + '"]').trigger('click');
                    } else {
                        // Activate the first tab by default
                        $('.office-addin-tabs .nav-tab').first().trigger('click');
                    }

                    // Gestisci il pulsante "Preview Changes"
                    $('#preview-changes').on('click', function(e) {
                        e.preventDefault();

                        console.log('Preview Changes button clicked');

                        // Cambia alla tab di anteprima
                        $('.office-addin-tabs .nav-tab[href="#preview-tab"]').trigger('click');

                        // Attendi un momento per assicurarsi che la tab sia completamente caricata
                        setTimeout(function() {
                            // Verifica se la funzione updatePreview è disponibile
                            if (typeof window.updatePreview === 'function') {
                                console.log('Calling updatePreview from Preview Changes button');
                                window.updatePreview();
                            } else {
                                console.error('updatePreview function not available for Preview Changes button');
                                alert('Impossibile aggiornare l\'anteprima. Ricarica la pagina e riprova.');
                            }
                        }, 300);
                    });

                    // As a fallback, also initialize tabs on window load to catch any late DOM rendering
                    $(window).on('load', function() {
                        // Re-check if tabs are correctly initialized
                        if (!$('.tab-content.active').length) {
                            console.log('Tab content not initialized correctly, reinitializing...');
                            $('.office-addin-tabs .nav-tab').first().trigger('click');
                        }
                    });

                    // Debug information
                    console.log('Tab initialization complete.');
                });
            </script>

            <!-- CSS for Office Add-in tabs -->
            <style>
                .office-addin-tabs .nav-tab-wrapper {
                    margin-bottom: 1em;
                }
                .office-addin-tabs .tab-content {
                    display: none;
                    padding: 10px;
                    background: #fff;
                    border: 1px solid #ccc;
                    border-top: none;
                }
                .office-addin-tabs .tab-content.active {
                    display: block;
                }
            </style>
        </div>
        <?php
    }

    /**
     * Get fallback Office Add-in HTML content
     *
     * Questo metodo fornisce un contenuto HTML di base per l'Office Add-in
     * nel caso in cui non sia possibile ottenere il contenuto predefinito dalla classe Document_Viewer_Settings
     *
     * @return string Contenuto HTML di base per l'Office Add-in
     */
    public function get_fallback_office_addin_content() {
        return '
<div class="excel-addin-container">
    <h2>Financial Advisor Excel Add-in</h2>

    <div class="section">
        <h3>1. Extract Text from Excel</h3>
        <p>Select cells in your Excel sheet and click the button below to extract text for analysis.</p>
        <button id="extract-text" class="primary-button" title="Extract text from selected cells in Excel">
            <span class="button-icon">📋</span> Extract Selected Cells
        </button>
        <div id="extracted-text-container" style="display:none;">
            <h4>Extracted Text:</h4>
            <div id="extracted-text" class="text-display"></div>
        </div>
    </div>

    <div class="section">
        <h3>2. Choose Analysis Question</h3>
        <div class="form-group">
            <label for="predefined-query">Select a predefined query:</label>
            <select id="predefined-query" class="full-width" title="Select from predefined analysis questions">
                <option value="">-- Select a query --</option>
                <option value="1">Analyze financial performance</option>
                <option value="2">Identify key trends</option>
                <option value="3">Summarize quarterly results</option>
                <option value="4">Compare regional performance</option>
            </select>
        </div>

        <div class="form-group">
            <label for="custom-query">Or enter your own question:</label>
            <textarea id="custom-query" rows="3" class="full-width" placeholder="Enter your analysis question here..." title="Enter a custom analysis question"></textarea>
        </div>

        <button id="analyze-button" class="primary-button" title="Send data for analysis">
            <span class="button-icon">🔍</span> Analyze
        </button>
    </div>

    <div class="section">
        <h3>3. Analysis Results</h3>
        <div id="analysis-results" class="results-container">
            <p class="placeholder">Analysis results will appear here after you extract text and submit your question.</p>
        </div>
    </div>

    <div class="section">
        <h3>Connection Status</h3>
        <div class="debug-info">
            <p><strong>API Status:</strong> <span id="api-status">Not connected</span></p>
            <p><strong>Selected Model:</strong> <span id="selected-model">None</span></p>
        </div>
    </div>

    <div class="footer">
        <p>This add-in connects to your Financial Advisor WordPress plugin and uses the same API settings.</p>
    </div>
</div>

<style>
.excel-addin-container {
    font-family: "Segoe UI", Arial, sans-serif;
    padding: 15px;
    max-width: 800px;
    margin: 0 auto;
}

.section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f9f9f9;
}

h2 {
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    text-align: center;
}

h3 {
    color: #3498db;
    margin-top: 0;
    margin-bottom: 15px;
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.full-width {
    width: 100%;
    box-sizing: border-box;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.primary-button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.primary-button:hover {
    background-color: #2980b9;
}

.button-icon {
    margin-right: 8px;
}

.secondary-button {
    background-color: #95a5a6;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.secondary-button:hover {
    background-color: #7f8c8d;
}

.text-display {
    border: 1px solid #ddd;
    padding: 10px;
    background-color: white;
    min-height: 100px;
    max-height: 200px;
    overflow-y: auto;
    margin-top: 10px;
}

.results-container {
    border: 1px solid #ddd;
    padding: 15px;
    background-color: white;
    min-height: 150px;
    max-height: 300px;
    overflow-y: auto;
}

.debug-info {
    font-size: 12px;
    color: #7f8c8d;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
}

.debug-info p {
    margin: 5px 0;
    flex: 1 0 200px;
}

.debug-info button {
    margin-top: 5px;
}

.placeholder {
    color: #95a5a6;
    font-style: italic;
    text-align: center;
    padding: 20px 0;
}

.footer {
    text-align: center;
    font-size: 12px;
    color: #7f8c8d;
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

/* Loading indicator */
.loading {
    text-align: center;
    padding: 20px;
}

.loading:after {
    content: ".";
    animation: dots 1s steps(5, end) infinite;
}

@keyframes dots {
    0%, 20% { content: "."; }
    40% { content: ".."; }
    60% { content: "..."; }
    80%, 100% { content: ""; }
}

/* Success and error messages */
.success-message {
    color: #27ae60;
    font-weight: bold;
}

.error-message {
    color: #e74c3c;
    font-weight: bold;
}
</style>
';
    }
}

/**
 * Funzione per accedere all'istanza del Menu Manager
 *
 * @return Financial_Advisor_Menu_Manager
 */
function financial_advisor_menu_manager() {
    return Financial_Advisor_Menu_Manager::get_instance();
}

// Inizializza il menu manager
financial_advisor_menu_manager();