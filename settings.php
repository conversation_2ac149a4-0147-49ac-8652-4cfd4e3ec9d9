<?php
if (!defined('ABSPATH')) {
    exit;
}

class Document_Viewer_Settings {
    public function __construct() {
        // Core settings hooks
        add_action('admin_init', array($this, 'register_settings'));

        // API test and save handlers
        add_action('wp_ajax_test_api_connection', array($this, 'test_api_connection'));
        add_action('wp_ajax_test_api_key', array($this, 'test_api_key'));
        add_action('wp_ajax_test_api_endpoint', array($this, 'test_api_endpoint'));
        add_action('wp_ajax_test_api_model', array($this, 'test_api_model'));
        add_action('wp_ajax_test_specific_model', array($this, 'test_specific_model'));
        add_action('wp_ajax_quick_save_model', array($this, 'quick_save_model'));
        add_action('wp_ajax_save_api_key', array($this, 'save_api_key'));
        add_action('wp_ajax_save_api_endpoint', array($this, 'save_api_endpoint'));
        add_action('wp_ajax_save_api_model', array($this, 'save_api_model'));
        add_action('wp_ajax_save_analysis_api_key', array($this, 'save_analysis_api_key'));
        add_action('wp_ajax_save_analysis_api_endpoint', array($this, 'save_analysis_api_endpoint'));
        add_action('wp_ajax_save_analysis_model', array($this, 'save_analysis_model'));
        add_action('wp_ajax_save_model_list', array($this, 'save_model_list'));

        // Debug log handlers
        add_action('wp_ajax_clear_debug_log', array($this, 'clear_debug_log'));
        add_action('wp_ajax_refresh_debug_log', array($this, 'refresh_debug_log'));

        // Chat and composer handlers
        add_action('wp_ajax_settings_chat_model', array($this, 'settings_chat_model'));
        add_action('wp_ajax_execute_composer', array($this, 'execute_composer'));

        // Predefined queries handlers
        add_action('wp_ajax_add_preset_query', array($this, 'add_preset_query'));
        add_action('wp_ajax_update_preset_query', array($this, 'update_preset_query'));
        add_action('wp_ajax_delete_preset_query', array($this, 'delete_preset_query'));
        add_action('wp_ajax_add_financial_query_set', array($this, 'add_financial_query_set'));

        $this->register_document_settings();
    }

    public function register_settings() {
        // Register API key setting
        register_setting(
            'document_viewer_settings',
            'document_viewer_api_key',
            'sanitize_text_field'
        );

        // Register API endpoint setting
        register_setting(
            'document_viewer_settings',
            'document_viewer_api_endpoint',
            'esc_url_raw'
        );

        // Register basic model setting
        register_setting(
            'document_viewer_settings',
            'document_viewer_model',
            'sanitize_text_field'
        );

        // Register and add model list settings
        register_setting(
            'document_viewer_settings',
            'document_viewer_model_list',
            array(
                'type' => 'array',
                'sanitize_callback' => array($this, 'sanitize_model_list')
            )
        );

        // Register analysis API settings
        register_setting(
            'document_viewer_settings',
            'document_viewer_analysis_api_key',
            'sanitize_text_field'
        );

        register_setting(
            'document_viewer_settings',
            'document_viewer_analysis_endpoint',
            'esc_url_raw'
        );

        register_setting(
            'document_viewer_settings',
            'document_viewer_analysis_model',
            'sanitize_text_field'
        );        // Google OAuth settings removed

        // Register Login Global Redirect URL setting
        register_setting(
            'document_viewer_settings',
            'login_global_redirect_url',
            'esc_url_raw'
        );
          // Abbiamo rimosso il setting login_redirect_inheritance perché non è più necessario

        // Register Help Line content setting
        register_setting(
            'document_viewer_settings',
            'help_line_content',
            'wp_kses_post'
        );

        // Register performance settings
        register_setting(
            'document_viewer_settings',
            'document_viewer_skip_permission_tests',
            'rest_sanitize_boolean'
        );
          // Developer mode settings removed

        // Add settings sections
        add_settings_section(
            'api_settings',
            __('API Settings', 'document-viewer-plugin'),
            array($this, 'api_settings_section_info'),
            'document-viewer-settings'
        );

        add_settings_section(
            'document_analysis_settings',
            __('Document Analysis Settings', 'document-viewer-plugin'),
            array($this, 'document_analysis_settings_section_info'),
            'document-viewer-settings'
        );

        add_settings_section(
            'help_line_settings',
            __('Help Line Settings', 'document-viewer-plugin'),
            array($this, 'help_line_settings_section_info'),
            'document-viewer-settings'
        );

        add_settings_section(
            'performance_settings',
            __('Performance Settings', 'document-viewer-plugin'),
            array($this, 'performance_settings_section_info'),
            'document-viewer-settings'
        );

        // Add settings fields
        $this->add_settings_fields();
    }

    protected function add_settings_fields() {
        // Primary API fields
        add_settings_field(
            'document_viewer_api_key',
            __('OpenRouter API Key', 'document-viewer-plugin'),
            array($this, 'api_key_field'),
            'document-viewer-settings',
            'api_settings'
        );

        add_settings_field(
            'document_viewer_api_endpoint',
            __('OpenRouter API Endpoint', 'document-viewer-plugin'),
            array($this, 'api_endpoint_field'),
            'document-viewer-settings',
            'api_settings'
        );

        add_settings_field(
            'document_viewer_model',
            __('Selected OpenRouter Model', 'document-viewer-plugin'),
            array($this, 'model_selection_field'),
            'document-viewer-settings',
            'api_settings'
        );

        // Analysis API fields
        add_settings_field(
            'document_viewer_analysis_api_key',
            __('Analysis API Key', 'document-viewer-plugin'),
            array($this, 'analysis_api_key_field'),
            'document-viewer-settings',
            'document_analysis_settings'
        );

        add_settings_field(
            'document_viewer_analysis_endpoint',
            __('Analysis API Endpoint', 'document-viewer-plugin'),
            array($this, 'analysis_api_endpoint_field'),
            'document-viewer-settings',
            'document_analysis_settings'
        );

        add_settings_field(
            'document_viewer_analysis_model',
            __('Analysis Model', 'document-viewer-plugin'),
            array($this, 'analysis_model_selection_field'),
            'document-viewer-settings',
            'document_analysis_settings'
        );

        // Help Line content field
        add_settings_field(
            'help_line_content',
            __('Help Line Content', 'document-viewer-plugin'),
            array($this, 'help_line_content_field'),
            'document-viewer-settings',
            'help_line_settings'
        );

        // Model list field
        add_settings_field(
            'document_viewer_model_list',
            __('OpenRouter Models List', 'document-viewer-plugin'),
            array($this, 'model_list_field'),
            'document-viewer-settings',
            'api_settings'
        );

        // Performance settings field
        add_settings_field(
            'document_viewer_skip_permission_tests',
            __('Directory Permissions', 'document-viewer-plugin'),
            array($this, 'skip_permission_tests_field'),
            'document-viewer-settings',
            'performance_settings'
        );
    }

    public function settings_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }
        // Get log content or set empty if not available
        $log_content = $this->get_debug_log_content();
        ?>
        <div class="wrap" style="margin-top:40px; position:relative;">
            <div style="display:flex; gap:20px;">
                <div style="flex:2;">
                    <div class="settings-header" style="display:flex; align-items:center; gap:15px; background:#fff; border:1px solid #ccc; border-radius:4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); padding:15px; position:relative; z-index:1;">
                        <img src="<?php echo plugin_dir_url(__FILE__) . 'assets/images/logo.png'; ?>"
                             alt="<?php _e('Plugin Logo', 'document-viewer-plugin'); ?>"
                             style="width:50px; height:50px; object-fit:contain;">
                        <h1 style="margin:0; padding:0; font-size:23px; line-height:1.3;"><?php _e('Financial Advisor Settings', 'document-viewer-plugin'); ?></h1>
                    </div>

                    <div class="settings-container" style="margin-top:20px; background:#fff; padding:25px; border:1px solid #ccc; border-radius:4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">                        <h2 class="nav-tab-wrapper">
                            <a href="#primary-api" class="nav-tab nav-tab-active"><?php _e('Primary API', 'document-viewer-plugin'); ?></a>
                            <a href="#analysis-api" class="nav-tab"><?php _e('Analysis API', 'document-viewer-plugin'); ?></a>
                            <a href="#model-list" class="nav-tab"><?php _e('Model List', 'document-viewer-plugin'); ?></a>
                            <a href="#preset-queries" class="nav-tab"><?php _e('Predefined Queries', 'document-viewer-plugin'); ?></a>
                            <a href="#help-line" class="nav-tab"><?php _e('Help Line', 'document-viewer-plugin'); ?></a>                            <a href="#document-settings" class="nav-tab"><?php _e('Document Settings', 'document-viewer-plugin'); ?></a>
                            <a href="#performance-settings" class="nav-tab"><?php _e('Performance Settings', 'document-viewer-plugin'); ?></a>
                        </h2>

                        <form method="post" action="options.php">
                            <?php settings_fields('document_viewer_settings'); ?>

                            <div id="primary-api" class="tab-content active">
                                <h3><?php _e('Primary API Settings', 'document-viewer-plugin'); ?></h3>
                                <?php
                                $this->api_key_field();
                                $this->api_endpoint_field();
                                $this->model_selection_field();
                                ?>
                            </div>

                            <div id="analysis-api" class="tab-content" style="display:none;">
                                <h3><?php _e('Analysis API Settings', 'document-viewer-plugin'); ?></h3>
                                <?php
                                $this->analysis_api_key_field();
                                $this->analysis_api_endpoint_field();
                                $this->analysis_model_selection_field();
                                ?>
                            </div>

                            <div id="model-list" class="tab-content" style="display:none;">
                                <h3><?php _e('Model List Management', 'document-viewer-plugin'); ?></h3>
                                <?php $this->model_list_field(); ?>
                            </div>

                            <div id="preset-queries" class="tab-content" style="display:none;">
                                <h3><?php _e('Predefined Queries Management', 'document-viewer-plugin'); ?></h3>

                                <p class="description"><?php _e('Create, edit, or delete predefined query templates that users can select when analyzing documents.', 'document-viewer-plugin'); ?></p>

                                <div id="preset-queries-container">
                                    <?php
                                    // Retrieve existing preset queries from database
                                    global $wpdb;
                                    $table_name = $wpdb->prefix . 'document_preset_queries';
                                    $preset_queries = $wpdb->get_results("SELECT * FROM $table_name ORDER BY title ASC", ARRAY_A);

                                    if (!empty($preset_queries)) {
                                        foreach ($preset_queries as $query) {
                                    ?>
                                    <div class="preset-query-entry" data-id="<?php echo esc_attr($query['id']); ?>">
                                        <div class="preset-query-row">
                                            <input type="text" class="preset-query-title regular-text" value="<?php echo esc_attr($query['title']); ?>" placeholder="<?php _e('Query Title', 'document-viewer-plugin'); ?>">
                                            <button type="button" class="toggle-query-text button button-secondary"><?php _e('Show/Hide Query', 'document-viewer-plugin'); ?></button>
                                            <button type="button" class="update-preset-query button button-primary"><?php _e('Update', 'document-viewer-plugin'); ?></button>
                                            <button type="button" class="delete-preset-query button button-secondary"><?php _e('Delete', 'document-viewer-plugin'); ?></button>
                                            <span class="preset-query-result"></span>
                                        </div>
                                        <div class="preset-query-text-container" style="display:none;">
                                            <textarea class="preset-query-text" rows="4"><?php echo esc_textarea($query['query_text']); ?></textarea>
                                        </div>
                                    </div>
                                    <?php
                                        }
                                    } else {
                                        echo '<p>' . __('No predefined queries found. Add some below.', 'document-viewer-plugin') . '</p>';
                                    }
                                    ?>
                                </div>

                                <h4><?php _e('Add New Predefined Query', 'document-viewer-plugin'); ?></h4>
                                <div class="add-new-preset-query">
                                    <div class="preset-query-row">
                                        <input type="text" id="new-preset-query-title" class="regular-text" placeholder="<?php _e('Query Title', 'document-viewer-plugin'); ?>">
                                    </div>
                                    <div class="preset-query-text-container">
                                        <textarea id="new-preset-query-text" rows="4" placeholder="<?php _e('Enter the predefined query text here...', 'document-viewer-plugin'); ?>"></textarea>
                                    </div>
                                    <button type="button" id="add-preset-query" class="button button-primary"><?php _e('Add Query', 'document-viewer-plugin'); ?></button>
                                    <span id="add-preset-query-result"></span>
                                </div>

                                <h4><?php _e('Bulk Add Financial Queries', 'document-viewer-plugin'); ?></h4>
                                <p class="description"><?php _e('Click the button below to add a set of commonly used financial analysis queries.', 'document-viewer-plugin'); ?></p>
                                <button type="button" id="add-financial-queries" class="button button-secondary"><?php _e('Add Financial Query Set', 'document-viewer-plugin'); ?></button>
                                <span id="add-financial-queries-result"></span>
                            </div>

                            <div id="help-line" class="tab-content" style="display:none;">
                                <h3><?php _e('Help Line Settings', 'document-viewer-plugin'); ?></h3>
                                <p class="description"><?php _e('Configure the content for the Help Line shown on document analysis pages.', 'document-viewer-plugin'); ?></p>
                                <?php $this->help_line_content_field(); ?>
                            </div>                            <div id="document-settings" class="tab-content" style="display:none;">
                                <h3><?php _e('Document Settings', 'document-viewer-plugin'); ?></h3>
                                <?php $this->render_document_api_settings(); ?>
                            </div>                            <div id="performance-settings" class="tab-content" style="display:none;">
                                <h3><?php _e('Performance Settings', 'document-viewer-plugin'); ?></h3>
                                <?php $this->skip_permission_tests_field(); ?>
                            </div>

                            <?php submit_button(); ?>
                        </form>
                    </div>

                    <div class="log-viewer-section" style="margin-top:20px; background:#fff; padding:25px; border:1px solid #ccc; border-radius:4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                        <div style="display:flex; justify-content:space-between; align-items:center; margin-bottom:15px;">
                            <div style="display:flex; align-items:center; gap:10px;">
                                <h2 style="margin:0;"><?php _e('Log Viewer', 'document-viewer-plugin'); ?></h2>
                                <button type="button" id="refresh-debug-log" class="button button-secondary" style="margin-left:10px;">
                                    <span class="dashicons dashicons-update" style="margin-top:3px;"></span>
                                    <?php _e('Refresh', 'document-viewer-plugin'); ?>
                                </button>
                                <div id="last-refresh" style="color:#666; font-size:12px; margin-left:10px;"></div>
                            </div>
                            <button type="button" id="clear-debug-log" class="button button-secondary">
                                <?php _e('Clear Log', 'document-viewer-plugin'); ?>
                            </button>
                        </div>
                        <textarea readonly id="debug-log-content" style="width:100%; height:300px; margin-top:15px; font-family:monospace; background:#f9f9f9;"><?php echo esc_textarea($log_content); ?></textarea>
                    </div>
                </div>

                <!-- Chat Widget Panel -->
                <div style="flex:1;">
                    <div class="chat-test-panel" style="position:sticky; top:40px; background:#fff; padding:20px; border:1px solid #ccc; border-radius:4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                        <h2 style="margin-top:0; padding-bottom:15px; border-bottom:1px solid #eee;">
                            <?php _e('Chat Test Panel', 'document-viewer-plugin'); ?>
                        </h2>
                        <div class="chat-widget">
                            <div id="settings-chat-log" style="height:400px; overflow-y:auto; border:1px solid #e5e5e5; padding:10px; margin:10px 0; background:#f9f9f9;"></div>
                            <div style="position:relative;">
                                <input type="text" id="settings-chat-input" placeholder="<?php _e('Test message...', 'document-viewer-plugin'); ?>" style="flex:1;">
                                <div id="settings-chat-typing" style="display:none; position:absolute; bottom:100%; left:0; padding:5px; font-size:12px; color:#666;">
                                    <span class="dots">...</span>
                                </div>
                            </div>
                            <button type="button" id="settings-send-chat" class="button button-primary"><?php _e('Send', 'document-viewer-plugin'); ?></button>
                            <button type="button" id="settings-test-chat-connection" class="button button-secondary" style="margin-top:10px; width:100%;">
                                <?php _e('Test Connection', 'document-viewer-plugin'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <style>
            /* Stili per i tab */
            .nav-tab-wrapper { margin-bottom: 20px; }
            .tab-content { display: none; }
            .tab-content.active { display: block; }
            .field-container { margin-bottom: 15px; }
            #wpcontent { padding-top: 0 !important; }
            .wrap { margin-right: 20px; }
            #settings-chat-input { padding: 8px 10px; width: 100%; }
            #settings-chat-log { font-size: 13px; line-height: 1.5; }
            #settings-chat-log p { margin: 5px 0; padding: 5px; border-bottom: 1px solid #eee; }
            .chat-test-panel { max-width: 400px; }
            @keyframes dot-typing {
                0% { content: ''; }
                25% { content: '.'; }
                50% { content: '..'; }
                75% { content: '...'; }
                100% { content: ''; }
            }
            .dots::after {
                content: '';
                display: inline-block;
                width: 16px;
                animation: dot-typing 1.5s infinite;
            }
            #settings-chat-typing {
                background: #f5f5f5;
                border-radius: 3px;
                border: 1px solid #ddd;
            }
            .input-with-test {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .test-result, .save-result {
                display: inline-block;
                min-width: 100px;
                font-style: italic;
            }
            .save-success {
                color: green;
                font-weight: bold;
            }
            .save-error {
                color: red;
                font-weight: bold;
            }
        </style>

        <script>
        jQuery(document).ready(function($) {
            // Tab handling - Fixed version
            $('.nav-tab').on('click', function(e) {
                e.preventDefault();
                const target = $(this).attr('href');

                // Update tabs
                $('.nav-tab').removeClass('nav-tab-active');
                $(this).addClass('nav-tab-active');

                // Update content
                $('.tab-content').hide();
                $(target).show();

                // Save active tab to localStorage
                localStorage.setItem('activeSettingsTab', target);
            });

            // Restore last active tab
            const lastTab = localStorage.getItem('activeSettingsTab');
            if (lastTab) {
                $(`.nav-tab[href="${lastTab}"]`).click();
            }

            // API Testing functions
            $('#test-api-key').on('click', function() {
                var apiKey = $('#api-key-input').val();
                var resultSpan = $('#api-key-test-result');

                if (!apiKey) {
                    resultSpan.html('<span style="color:red;"><?php _e('API key is required', 'document-viewer-plugin'); ?></span>');
                    return;
                }

                resultSpan.html('<span style="color:blue;"><?php _e('Testing...', 'document-viewer-plugin'); ?></span>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'test_api_key',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        api_key: apiKey
                    },
                    success: function(response) {
                        if (response.success) {
                            resultSpan.html('<span style="color:green;">' + response.data.message + '</span>');
                        } else {
                            resultSpan.html('<span style="color:red;">' + response.data.message + '</span>');
                        }
                    },
                    error: function() {
                        resultSpan.html('<span style="color:red;"><?php _e('Connection error', 'document-viewer-plugin'); ?></span>');
                    }
                });
            });

            $('#test-api-endpoint').on('click', function() {
                var apiEndpoint = $('#api-endpoint-input').val();
                var resultSpan = $('#api-endpoint-test-result');

                if (!apiEndpoint) {
                    resultSpan.html('<span style="color:red;"><?php _e('API endpoint is required', 'document-viewer-plugin'); ?></span>');
                    return;
                }

                resultSpan.html('<span style="color:blue;"><?php _e('Testing...', 'document-viewer-plugin'); ?></span>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'test_api_endpoint',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        api_endpoint: apiEndpoint
                    },
                    success: function(response) {
                        if (response.success) {
                            resultSpan.html('<span style="color:green;">' + response.data.message + '</span>');
                        } else {
                            resultSpan.html('<span style="color:red;">' + response.data.message + '</span>');
                        }
                    },
                    error: function() {
                        resultSpan.html('<span style="color:red;"><?php _e('Connection error', 'document-viewer-plugin'); ?></span>');
                    }
                });
            });

            $('#test-api-model').on('click', function() {
                var model = $('#quick-model-select').val();
                var resultSpan = $('#api-model-test-result');

                if (!model) {
                    resultSpan.html('<span style="color:red;"><?php _e('Please select a model', 'document-viewer-plugin'); ?></span>');
                    return;
                }

                resultSpan.html('<span style="color:blue;"><?php _e('Testing...', 'document-viewer-plugin'); ?></span>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'test_api_model',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        model: model
                    },
                    success: function(response) {
                        if (response.success) {
                            resultSpan.html('<span style="color:green;">' + response.data.message + '</span>');
                        } else {
                            resultSpan.html('<span style="color:red;">' + response.data.message + '</span>');
                        }
                    },
                    error: function() {
                        resultSpan.html('<span style="color:red;"><?php _e('Connection error', 'document-viewer-plugin'); ?></span>');
                    }
                });
            });

            // Analysis API Testing functions
            $('#test-analysis-api-key').on('click', function() {
                var apiKey = $('#analysis-api-key-input').val();
                var resultSpan = $('#analysis-api-key-test-result');

                if (!apiKey) {
                    resultSpan.html('<span style="color:red;"><?php _e('API key is required', 'document-viewer-plugin'); ?></span>');
                    return;
                }

                resultSpan.html('<span style="color:blue;"><?php _e('Testing...', 'document-viewer-plugin'); ?></span>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'test_api_key',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        api_key: apiKey,
                        is_analysis: true
                    },
                    success: function(response) {
                        if (response.success) {
                            resultSpan.html('<span style="color:green;">' + response.data.message + '</span>');
                        } else {
                            resultSpan.html('<span style="color:red;">' + response.data.message + '</span>');
                        }
                    },
                    error: function() {
                        resultSpan.html('<span style="color:red;"><?php _e('Connection error', 'document-viewer-plugin'); ?></span>');
                    }
                });
            });

            $('#test-analysis-api-endpoint').on('click', function() {
                var apiEndpoint = $('#analysis-api-endpoint-input').val();
                var resultSpan = $('#analysis-api-endpoint-test-result');

                if (!apiEndpoint) {
                    resultSpan.html('<span style="color:red;"><?php _e('API endpoint is required', 'document-viewer-plugin'); ?></span>');
                    return;
                }

                resultSpan.html('<span style="color:blue;"><?php _e('Testing...', 'document-viewer-plugin'); ?></span>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'test_api_endpoint',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        api_endpoint: apiEndpoint,
                        is_analysis: true
                    },
                    success: function(response) {
                        if (response.success) {
                            resultSpan.html('<span style="color:green;">' + response.data.message + '</span>');
                        } else {
                            resultSpan.html('<span style="color:red;">' + response.data.message + '</span>');
                        }
                    },
                    error: function() {
                        resultSpan.html('<span style="color:red;"><?php _e('Connection error', 'document-viewer-plugin'); ?></span>');
                    }
                });
            });

            $('#test-analysis-model').on('click', function() {
                var model = $('#analysis-model-select').val();
                var resultSpan = $('#analysis-model-test-result');

                if (!model) {
                    resultSpan.html('<span style="color:red;"><?php _e('Please select a model', 'document-viewer-plugin'); ?></span>');
                    return;
                }

                resultSpan.html('<span style="color:blue;"><?php _e('Testing...', 'document-viewer-plugin'); ?></span>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'test_api_model',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        model: model,
                        is_analysis: true
                    },
                    success: function(response) {
                        if (response.success) {
                            resultSpan.html('<span style="color:green;">' + response.data.message + '</span>');
                        } else {
                            resultSpan.html('<span style="color:red;">' + response.data.message + '</span>');
                        }
                    },
                    error: function() {
                        resultSpan.html('<span style="color:red;"><?php _e('Connection error', 'document-viewer-plugin'); ?></span>');
                    }
                });
            });

            // Individual save functions
            $('#save-api-key').on('click', function() {
                var apiKey = $('#api-key-input').val();
                var resultSpan = $('#api-key-result');

                if (!apiKey) {
                    resultSpan.html('<span class="save-error"><?php _e('API key is required', 'document-viewer-plugin'); ?></span>');
                    return;
                }

                resultSpan.html('<span style="color:blue;"><?php _e('Saving...', 'document-viewer-plugin'); ?></span>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'save_api_key',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        api_key: apiKey
                    },
                    success: function(response) {
                        if (response.success) {
                            resultSpan.html('<span class="save-success">' + response.data.message + '</span>');
                            setTimeout(function() {
                                resultSpan.html('');
                            }, 3000);
                        } else {
                            resultSpan.html('<span class="save-error">' + response.data.message + '</span>');
                        }
                    },
                    error: function() {
                        resultSpan.html('<span class="save-error"><?php _e('Error saving', 'document-viewer-plugin'); ?></span>');
                    }
                });
            });

            $('#save-api-endpoint').on('click', function() {
                var apiEndpoint = $('#api-endpoint-input').val();
                var resultSpan = $('#api-endpoint-result');

                if (!apiEndpoint) {
                    resultSpan.html('<span class="save-error"><?php _e('API endpoint is required', 'document-viewer-plugin'); ?></span>');
                    return;
                }

                resultSpan.html('<span style="color:blue;"><?php _e('Saving...', 'document-viewer-plugin'); ?></span>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'save_api_endpoint',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        api_endpoint: apiEndpoint
                    },
                    success: function(response) {
                        if (response.success) {
                            resultSpan.html('<span class="save-success">' + response.data.message + '</span>');
                            setTimeout(function() {
                                resultSpan.html('');
                            }, 3000);
                        } else {
                            resultSpan.html('<span class="save-error">' + response.data.message + '</span>');
                        }
                    },
                    error: function() {
                        resultSpan.html('<span class="save-error"><?php _e('Error saving', 'document-viewer-plugin'); ?></span>');
                    }
                });
            });

            $('#save-api-model').on('click', function() {
                var model = $('#quick-model-select').val();
                var resultSpan = $('#api-model-result');

                if (!model) {
                    resultSpan.html('<span class="save-error"><?php _e('Please select a model', 'document-viewer-plugin'); ?></span>');
                    return;
                }

                resultSpan.html('<span style="color:blue;"><?php _e('Saving...', 'document-viewer-plugin'); ?></span>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'save_api_model',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        model: model
                    },
                    success: function(response) {
                        if (response.success) {
                            resultSpan.html('<span class="save-success">' + response.data.message + '</span>');
                            setTimeout(function() {
                                resultSpan.html('');
                            }, 3000);
                        } else {
                            resultSpan.html('<span class="save-error">' + response.data.message + '</span>');
                        }
                    },
                    error: function() {
                        resultSpan.html('<span class="save-error"><?php _e('Error saving', 'document-viewer-plugin'); ?></span>');
                    }
                });
            });

            // Analysis API save functions
            $('#save-analysis-api-key').on('click', function() {
                var apiKey = $('#analysis-api-key-input').val();
                var resultSpan = $('#analysis-api-key-result');

                if (!apiKey) {
                    resultSpan.html('<span class="save-error"><?php _e('API key is required', 'document-viewer-plugin'); ?></span>');
                    return;
                }

                resultSpan.html('<span style="color:blue;"><?php _e('Saving...', 'document-viewer-plugin'); ?></span>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'save_analysis_api_key',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        api_key: apiKey
                    },
                    success: function(response) {
                        if (response.success) {
                            resultSpan.html('<span class="save-success">' + response.data.message + '</span>');
                            setTimeout(function() {
                                resultSpan.html('');
                            }, 3000);
                        } else {
                            resultSpan.html('<span class="save-error">' + response.data.message + '</span>');
                        }
                    },
                    error: function() {
                        resultSpan.html('<span class="save-error"><?php _e('Error saving', 'document-viewer-plugin'); ?></span>');
                    }
                });
            });

            $('#save-analysis-api-endpoint').on('click', function() {
                var apiEndpoint = $('#analysis-api-endpoint-input').val();
                var resultSpan = $('#analysis-api-endpoint-result');

                if (!apiEndpoint) {
                    resultSpan.html('<span class="save-error"><?php _e('API endpoint is required', 'document-viewer-plugin'); ?></span>');
                    return;
                }

                resultSpan.html('<span style="color:blue;"><?php _e('Saving...', 'document-viewer-plugin'); ?></span>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'save_analysis_api_endpoint',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        api_endpoint: apiEndpoint
                    },
                    success: function(response) {
                        if (response.success) {
                            resultSpan.html('<span class="save-success">' + response.data.message + '</span>');
                            setTimeout(function() {
                                resultSpan.html('');
                            }, 3000);
                        } else {
                            resultSpan.html('<span class="save-error">' + response.data.message + '</span>');
                        }
                    },
                    error: function() {
                        resultSpan.html('<span class="save-error"><?php _e('Error saving', 'document-viewer-plugin'); ?></span>');
                    }
                });
            });

            $('#save-analysis-model').on('click', function() {
                var model = $('#analysis-model-select').val();
                var resultSpan = $('#analysis-model-result');

                resultSpan.html('<span style="color:blue;"><?php _e('Saving...', 'document-viewer-plugin'); ?></span>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'save_analysis_model',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        model: model
                    },
                    success: function(response) {
                        if (response.success) {
                            resultSpan.html('<span class="save-success">' + response.data.message + '</span>');
                            setTimeout(function() {
                                resultSpan.html('');
                            }, 3000);
                        } else {
                            resultSpan.html('<span class="save-error">' + response.data.message + '</span>');
                        }
                    },
                    error: function() {
                        resultSpan.html('<span class="save-error"><?php _e('Error saving', 'document-viewer-plugin'); ?></span>');
                    }
                });
            });

            // Other existing JavaScript functions
            function updateLastRefreshTime() {
                var now = new Date();
                var timeStr = now.getHours().toString().padStart(2, '0') + ':' +
                            now.getMinutes().toString().padStart(2, '0') + ':' +
                            now.getSeconds().toString().padStart(2, '0');
                $('#last-refresh').text('Last refresh: ' + timeStr);
            }

            function refreshLog() {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'refresh_debug_log',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>'
                    },
                    success: function(response) {
                        if(response.success) {
                            $('#debug-log-content').val(response.data.content);
                            updateLastRefreshTime();
                        }
                    }
                });
            }

            $('#refresh-debug-log').on('click', function() {
                refreshLog();
            });

            setInterval(refreshLog, 30000);
            updateLastRefreshTime();

            $('#settings-send-chat, #settings-test-chat-connection').on('click', function() {
                var message = $(this).attr('id') === 'settings-test-chat-connection' ? 'ping' : $('#settings-chat-input').val();
                if (!message) return;

                $('#settings-chat-log').append('<p><strong>You:</strong> ' + message + '</p>');
                $('#settings-chat-log').scrollTop($('#settings-chat-log')[0].scrollHeight);
                $('#settings-chat-typing').show();

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'settings_chat_model',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        chat_message: message
                    },
                    success: function(response) {
                        $('#settings-chat-typing').hide();
                        if(response.success) {
                            $('#settings-chat-log').append('<p><strong>Model:</strong> ' + response.data.reply + '</p>');
                        } else {
                            $('#settings-chat-log').append('<p><strong>Error:</strong> ' + response.data.message + '</p>');
                        }
                        $('#settings-chat-log').scrollTop($('#settings-chat-log')[0].scrollHeight);
                    },
                    error: function(xhr, status, error) {
                        $('#settings-chat-typing').hide();
                        $('#settings-chat-log').append('<p><strong>Error:</strong> ' + error + '</p>');
                        $('#settings-chat-log').scrollTop($('#settings-chat-log')[0].scrollHeight);
                    }
                });

                $('#settings-chat-input').val('');
            });

            $('#settings-chat-input').on('keypress', function(e) {
                if(e.which == 13) {
                    $('#settings-send-chat').click();
                }
            });

            $('#clear-debug-log').on('click', function() {
                if (confirm('<?php _e("Are you sure you want to clear the debug log?", "document-viewer-plugin"); ?>')) {
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'clear_debug_log',
                            nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>'
                        },
                        beforeSend: function() {
                            $('#clear-debug-log').prop('disabled', true).text('<?php _e("Clearing...", "document-viewer-plugin"); ?>');
                        },
                        success: function(response) {
                            $('#clear-debug-log').prop('disabled', false).text('<?php _e("Clear Log", "document-viewer-plugin"); ?>');

                            if (response.success) {
                                $('#debug-log-content').val('');
                                updateLastRefreshTime();
                                // Show success message
                                alert(response.data.message);
                            } else {
                                // Show error message
                                alert(response.data.message || '<?php _e("An error occurred", "document-viewer-plugin"); ?>');
                            }
                        },
                        error: function() {
                            $('#clear-debug-log').prop('disabled', false).text('<?php _e("Clear Log", "document-viewer-plugin"); ?>');
                            alert('<?php _e("Connection error", "document-viewer-plugin"); ?>');
                        }
                    });
                }
            });

            // JavaScript to handle predefined queries management
            $(document).ready(function() {
                // Toggle query text display when the show/hide button is clicked
                $(document).on('click', '.toggle-query-text', function() {
                    $(this).closest('.preset-query-entry').find('.preset-query-text-container').slideToggle();
                });

                // Update preset query
                $(document).on('click', '.update-preset-query', function() {
                    const $entry = $(this).closest('.preset-query-entry');
                    const queryId = $entry.data('id');
                    const title = $entry.find('.preset-query-title').val();
                    const queryText = $entry.find('.preset-query-text').val();
                    const $resultSpan = $entry.find('.preset-query-result');

                    if (!title || !queryText) {
                        $resultSpan.html('<span style="color:red;"><?php _e('Both title and query text are required', 'document-viewer-plugin'); ?></span>');
                        return;
                    }

                    $resultSpan.html('<span style="color:blue;"><?php _e('Updating...', 'document-viewer-plugin'); ?></span>');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'update_preset_query',
                            nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                            query_id: queryId,
                            title: title,
                            query_text: queryText
                        },
                        success: function(response) {
                            if (response.success) {
                                $resultSpan.html('<span style="color:green;">' + response.data.message + '</span>');
                                setTimeout(function() {
                                    $resultSpan.html('');
                                }, 3000);
                            } else {
                                $resultSpan.html('<span style="color:red;">' + response.data.message + '</span>');
                            }
                        },
                        error: function() {
                            $resultSpan.html('<span style="color:red;"><?php _e('Error updating query', 'document-viewer-plugin'); ?></span>');
                        }
                    });
                });

                // Delete preset query
                $(document).on('click', '.delete-preset-query', function() {
                    if (!confirm('<?php _e("Are you sure you want to delete this predefined query?", "document-viewer-plugin"); ?>')) {
                        return;
                    }

                    const $entry = $(this).closest('.preset-query-entry');
                    const queryId = $entry.data('id');
                    const $resultSpan = $entry.find('.preset-query-result');

                    $resultSpan.html('<span style="color:blue;"><?php _e('Deleting...', 'document-viewer-plugin'); ?></span>');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'delete_preset_query',
                            nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                            query_id: queryId
                        },
                        success: function(response) {
                            if (response.success) {
                                $entry.slideUp(400, function() {
                                    $(this).remove();
                                    // If no queries left, show message
                                    if ($('.preset-query-entry').length === 0) {
                                        $('#preset-queries-container').html('<p><?php _e("No predefined queries found. Add some below.", "document-viewer-plugin"); ?></p>');
                                    }
                                });
                            } else {
                                $resultSpan.html('<span style="color:red;">' + response.data.message + '</span>');
                            }
                        },
                        error: function() {
                            $resultSpan.html('<span style="color:red;"><?php _e('Error deleting query', 'document-viewer-plugin'); ?></span>');
                        }
                    });
                });

                // Add new preset query
                $('#add-preset-query').on('click', function() {
                    const title = $('#new-preset-query-title').val();
                    const queryText = $('#new-preset-query-text').val();
                    const $resultSpan = $('#add-preset-query-result');

                    if (!title || !queryText) {
                        $resultSpan.html('<span style="color:red;"><?php _e('Both title and query text are required', 'document-viewer-plugin'); ?></span>');
                        return;
                    }

                    $resultSpan.html('<span style="color:blue;"><?php _e('Adding...', 'document-viewer-plugin'); ?></span>');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'add_preset_query',
                            nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                            title: title,
                            query_text: queryText
                        },
                        success: function(response) {
                            if (response.success) {
                                // Clear form fields
                                $('#new-preset-query-title').val('');
                                $('#new-preset-query-text').val('');

                                // Show success message
                                $resultSpan.html('<span style="color:green;">' + response.data.message + '</span>');

                                // Add the new entry to the list
                                const newEntry = `
                                    <div class="preset-query-entry" data-id="${response.data.query_id}">
                                        <div class="preset-query-row">
                                            <input type="text" class="preset-query-title regular-text" value="${escapeHtml(title)}" placeholder="<?php _e('Query Title', 'document-viewer-plugin'); ?>">
                                            <button type="button" class="toggle-query-text button button-secondary"><?php _e('Show/Hide Query', 'document-viewer-plugin'); ?></button>
                                            <button type="button" class="update-preset-query button button-primary"><?php _e('Update', 'document-viewer-plugin'); ?></button>
                                            <button type="button" class="delete-preset-query button button-secondary"><?php _e('Delete', 'document-viewer-plugin'); ?></button>
                                            <span class="preset-query-result"></span>
                                        </div>
                                        <div class="preset-query-text-container" style="display:none;">
                                            <textarea class="preset-query-text" rows="4">${escapeHtml(queryText)}</textarea>
                                        </div>
                                    </div>
                                `;

                                // If this is the first entry, clear the "no queries" message
                                if ($('.preset-query-entry').length === 0) {
                                    $('#preset-queries-container').html('');
                                }

                                $('#preset-queries-container').append(newEntry);

                                // Hide success message after a delay
                                setTimeout(function() {
                                    $resultSpan.html('');
                                }, 3000);
                            } else {
                                $resultSpan.html('<span style="color:red;">' + response.data.message + '</span>');
                            }
                        },
                        error: function() {
                            $resultSpan.html('<span style="color:red;"><?php _e('Error adding query', 'document-viewer-plugin'); ?></span>');
                        }
                    });
                });

                // Add financial query set
                $('#add-financial-queries').on('click', function() {
                    if (!confirm('<?php _e("This will add a set of predefined financial queries. Continue?", "document-viewer-plugin"); ?>')) {
                        return;
                    }

                    const $resultSpan = $('#add-financial-queries-result');
                    $resultSpan.html('<span style="color:blue;"><?php _e('Adding financial queries...', 'document-viewer-plugin'); ?></span>');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'add_financial_query_set',
                            nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                $resultSpan.html('<span style="color:green;">' + response.data.message + '</span>');

                                // Reload the page to show the new queries
                                setTimeout(function() {
                                    location.reload();
                                }, 2000);
                            } else {
                                $resultSpan.html('<span style="color:red;">' + response.data.message + '</span>');
                            }
                        },
                        error: function() {
                            $resultSpan.html('<span style="color:red;"><?php _e('Error adding financial queries', 'document-viewer-plugin'); ?></span>');
                        }
                    });
                });

                // Helper function to escape HTML
                function escapeHtml(str) {
                    return str
                        .replace(/&/g, "&amp;")
                        .replace(/</g, "&lt;")
                        .replace(/>/g, "&gt;")
                        .replace(/"/g, "&quot;")
                        .replace(/'/g, "&#039;");
                }
            });

            // Aggiungiamo supporto ai tab di analisi indipendenti
            // Gestione dei tab di analisi nella pagina delle impostazioni
            $(document).on('click', '.analysis-tab', function() {
                const tabId = $(this).data('tab');

                // Aggiorniamo i tab
                $('.analysis-tab').removeClass('active');
                $(this).addClass('active');

                // Aggiorniamo i contenuti
                $('.tab-content').hide();
                $('#' + tabId).show();
            });
        }); // Aggiunta la chiusura mancante della funzione jQuery ready
        </script>
        <?php
    }

    public function render_console_page() {
        if (!current_user_can('administrator')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }
        ?>
        <div class="wrap">
            <h2><?php _e('Plugin Console', 'document-viewer-plugin'); ?></h2>

            <div class="console-section">
                <h3><?php _e('Composer Commands', 'document-viewer-plugin'); ?></h3>
                <button type="button" class="button button-primary execute-composer" data-command="install">
                    composer install
                </button>
                <button type="button" class="button button-secondary execute-composer" data-command="update">
                    composer update
                </button>
                <div id="composer-output" class="console-output"></div>
            </div>
        </div>

        <style>
        .console-output {
            background: #000;
            color: #fff;
            padding: 10px;
            margin: 10px 0;
            height: 300px;
            overflow: auto;
            font-family: monospace;
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            $('.execute-composer').on('click', function() {
                var command = $(this).data('command');
                var output = $('#composer-output');

                output.append('Executing: composer ' + command + '\n');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'execute_composer',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        command: command
                    },
                    success: function(response) {
                        if (response.success) {
                            output.append(response.data.output + '\n');
                        } else {
                            output.append('Error: ' + response.data.message + '\n');
                        }
                        output.scrollTop(output[0].scrollHeight);
                    }
                });
            });
        });
        </script>
        <?php
    }

    public function api_settings_section_info() {
        _e('Enter your OpenRouter API key and endpoint below:', 'document-viewer-plugin');
    }

    public function document_analysis_settings_section_info() {
        _e('Configure document analysis API settings below:', 'document-viewer-plugin');
    }

    public function help_line_settings_section_info() {
        if (!current_user_can('administrator')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }
        _e('Configure the content for the Help Line below:', 'document-viewer-plugin');
    }

    public function performance_settings_section_info() {
        _e('Configure performance-related settings below:', 'document-viewer-plugin');
    }

    public function api_key_field() {
        $api_key = get_option('document_viewer_api_key');
        ?>
        <div class="field-container" style="margin-bottom:15px;">
            <div class="input-with-test">
                <input type="text" name="document_viewer_api_key" id="api-key-input"
                       value="<?php echo esc_attr($api_key); ?>" class="regular-text"
                       pattern="[A-Za-z0-9\-_]+"
                       title="<?php _e('API key should contain only letters, numbers, hyphens, and underscores', 'document-viewer-plugin'); ?>"
                       required>
                <button type="button" id="save-api-key" class="button button-primary">
                    <?php _e('Save', 'document-viewer-plugin'); ?>
                </button>
                <button type="button" id="test-api-key" class="button button-secondary">
                    <?php _e('Test Key', 'document-viewer-plugin'); ?>
                </button>
                <span id="api-key-test-result" class="test-result"></span>
                <span id="api-key-result" class="save-result"></span>
            </div>
            <p class="description"><?php _e('Enter a valid OpenRouter API key', 'document-viewer-plugin'); ?></p>
        </div>
        <?php
    }

    public function api_endpoint_field() {
        $api_endpoint = get_option('document_viewer_api_endpoint');
        $default_endpoint = 'https://openrouter.ai/api/v1';
        if (empty($api_endpoint)) {
            $api_endpoint = $default_endpoint;
        }
        ?>
        <div class="field-container" style="margin-bottom:15px;">
            <div class="input-with-test">
                <input type="url" name="document_viewer_api_endpoint" id="api-endpoint-input"
                       value="<?php echo esc_attr($api_endpoint); ?>" class="regular-text"
                       placeholder="<?php echo esc_attr($default_endpoint); ?>"
                       required>
                <button type="button" id="save-api-endpoint" class="button button-primary">
                    <?php _e('Save', 'document-viewer-plugin'); ?>
                </button>
                <button type="button" id="test-api-endpoint" class="button button-secondary">
                    <?php _e('Test Endpoint', 'document-viewer-plugin'); ?>
                </button>
                <span id="api-endpoint-test-result" class="test-result"></span>
                <span id="api-endpoint-result" class="save-result"></span>
            </div>
            <p class="description"><?php _e('Enter a valid API endpoint URL', 'document-viewer-plugin'); ?></p>
        </div>
        <?php
    }

    public function model_selection_field() {
        $current_model = get_option('document_viewer_model');
        $model_list = get_option('document_viewer_model_list', array());
        ?>
        <div class="model-selection-wrapper" style="margin-bottom:15px;">
            <div class="input-with-test">
                <select name="document_viewer_model" id="quick-model-select" class="regular-text" required>
                    <option value=""><?php _e('Select a model', 'document-viewer-plugin'); ?></option>
                    <?php foreach ($model_list as $model): ?>
                        <option value="<?php echo esc_attr($model); ?>" <?php selected($current_model, $model); ?>>
                            <?php echo esc_html($model); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <button type="button" id="save-api-model" class="button button-primary">
                    <?php _e('Save', 'document-viewer-plugin'); ?>
                </button>
                <button type="button" id="test-api-model" class="button button-secondary">
                    <?php _e('Test Model', 'document-viewer-plugin'); ?>
                </button>
                <span id="api-model-test-result" class="test-result"></span>
                <span id="api-model-result" class="save-result"></span>
            </div>
            <p class="description"><?php _e('Select a model from the list or add new ones in the Model List tab', 'document-viewer-plugin'); ?></p>
        </div>
        <?php
    }

    public function analysis_api_key_field() {
        $api_key = get_option('document_viewer_analysis_api_key');
        ?>
        <div class="field-container" style="margin-bottom:15px;">
            <div class="input-with-test">
                <input type="text" name="document_viewer_analysis_api_key" id="analysis-api-key-input"
                       value="<?php echo esc_attr($api_key); ?>" class="regular-text"
                       pattern="[A-Za-z0-9\-_]+"
                       title="<?php _e('API key should contain only letters, numbers, hyphens, and underscores', 'document-viewer-plugin'); ?>">
                <button type="button" id="save-analysis-api-key" class="button button-primary">
                    <?php _e('Save', 'document-viewer-plugin'); ?>
                </button>
                <button type="button" id="test-analysis-api-key" class="button button-secondary">
                    <?php _e('Test Key', 'document-viewer-plugin'); ?>
                </button>
                <span id="analysis-api-key-test-result" class="test-result"></span>
                <span id="analysis-api-key-result" class="save-result"></span>
            </div>
            <p class="description"><?php _e('Enter a valid Analysis API key', 'document-viewer-plugin'); ?></p>
        </div>
        <?php
    }

    public function analysis_api_endpoint_field() {
        $api_endpoint = get_option('document_viewer_analysis_endpoint');
        ?>
        <div class="field-container" style="margin-bottom:15px;">
            <div class="input-with-test">
                <input type="url" name="document_viewer_analysis_endpoint" id="analysis-api-endpoint-input"
                       value="<?php echo esc_attr($api_endpoint); ?>" class="regular-text">
                <button type="button" id="save-analysis-api-endpoint" class="button button-primary">
                    <?php _e('Save', 'document-viewer-plugin'); ?>
                </button>
                <button type="button" id="test-analysis-api-endpoint" class="button button-secondary">
                    <?php _e('Test Endpoint', 'document-viewer-plugin'); ?>
                </button>
                <span id="analysis-api-endpoint-test-result" class="test-result"></span>
                <span id="analysis-api-endpoint-result" class="save-result"></span>
            </div>
            <p class="description"><?php _e('Enter a valid API endpoint URL', 'document-viewer-plugin'); ?></p>
        </div>
        <?php
    }

    public function analysis_model_selection_field() {
        $current_model = get_option('document_viewer_analysis_model');
        $model_list = get_option('document_viewer_model_list', array());
        ?>
        <div class="model-selection-wrapper" style="margin-bottom:15px;">
            <div class="input-with-test">
                <select name="document_viewer_analysis_model" id="analysis-model-select" class="regular-text">
                    <option value=""><?php _e('Select a model', 'document-viewer-plugin'); ?></option>
                    <?php foreach ($model_list as $model): ?>
                        <option value="<?php echo esc_attr($model); ?>" <?php selected($current_model, $model); ?>>
                            <?php echo esc_html($model); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <button type="button" id="save-analysis-model" class="button button-primary">
                    <?php _e('Save', 'document-viewer-plugin'); ?>
                </button>
                <button type="button" id="test-analysis-model" class="button button-secondary">
                    <?php _e('Test Model', 'document-viewer-plugin'); ?>
                </button>
                <span id="analysis-model-test-result" class="test-result"></span>
                <span id="analysis-model-result" class="save-result"></span>
            </div>
            <p class="description"><?php _e('Select a model for document analysis', 'document-viewer-plugin'); ?></p>
        </div>
        <?php
    }

    public function model_list_field() {
        $model_list = get_option('document_viewer_model_list', array());
        ?>
        <div id="model-list-container">
            <?php foreach ($model_list as $index => $model): ?>
            <div class="model-entry">
                <input type="text" name="document_viewer_model_list[]" value="<?php echo esc_attr($model); ?>" class="regular-text">
                <button type="button" class="remove-model button">Remove</button>
                <button type="button" class="test-model button" data-model="<?php echo esc_attr($model); ?>">Test Model</button>
                <span class="model-test-result"></span>
            </div>
            <?php endforeach; ?>
        </div>
        <button type="button" id="add-model" class="button">Add Model</button>
        <button type="button" id="save-models-directly" class="button button-primary" style="margin-left: 10px;">Save Models</button>
        <span id="model-list-save-result" style="margin-left: 10px;"></span>
        <script>
        jQuery(document).ready(function($) {
            $('#add-model').on('click', function() {
                var newEntry = $('<div class="model-entry">' +
                    '<input type="text" name="document_viewer_model_list[]" value="" class="regular-text">' +
                    '<button type="button" class="remove-model button">Remove</button>' +
                    '<button type="button" class="test-model button">Test Model</button>' +
                    '<span class="model-test-result"></span>' +
                    '</div>');
                $('#model-list-container').append(newEntry);
            });

            $(document).on('click', '.remove-model', function() {
                $(this).parent('.model-entry').remove();
            });

            $(document).on('click', '.test-model', function() {
                var button = $(this);
                var model = button.prev().prev().val();
                var resultSpan = button.next('.model-test-result');

                resultSpan.html('Testing...');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'test_specific_model',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        model: model
                    },
                    success: function(response) {
                        if(response.success) {
                            resultSpan.html('<span style="color:green;">' + response.data.message + '</span>');
                        } else {
                            resultSpan.html('<span style="color:red;">' + response.data.message + '</span>');
                        }
                    }
                });
            });

            // Aggiungi una funzione di salvataggio diretto
            $('#save-models-directly').on('click', function() {
                var models = [];
                var resultSpan = $('#model-list-save-result');

                // Raccoglie tutti i valori dei modelli dagli input
                $('input[name="document_viewer_model_list[]"]').each(function() {
                    var value = $(this).val().trim();
                    if (value) {
                        models.push(value);
                    }
                });

                resultSpan.html('<span style="color:blue;"><?php _e('Saving...', 'document-viewer-plugin'); ?></span>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'save_model_list',
                        nonce: '<?php echo wp_create_nonce("document_viewer_nonce"); ?>',
                        models: models
                    },
                    success: function(response) {
                        if(response.success) {
                            resultSpan.html('<span style="color:green;">' + response.data.message + '</span>');
                            // Dopo 3 secondi, ricarica la pagina per mostrare i modelli aggiornati
                            setTimeout(function() {
                                resultSpan.html('');
                                // Ricarica la pagina mantenendo la tab attiva
                                var currentTab = $('.nav-tab-active').attr('href');
                                window.location.href = window.location.href + (window.location.href.indexOf('?') > -1 ? '&' : '?') + 'tab=' + currentTab.substring(1);
                            }, 2000);
                        } else {
                            resultSpan.html('<span style="color:red;">' + response.data.message + '</span>');
                        }
                    },
                    error: function(xhr, status, error) {
                        resultSpan.html('<span style="color:red;"><?php _e('Error saving models', 'document-viewer-plugin'); ?>: ' + error + '</span>');
                        console.error("AJAX Error:", xhr.responseText);
                    }
                });
            });
        });
        </script>
        <style>
            .model-entry { margin-bottom: 10px; }
            .remove-model, .test-model { margin-left: 10px; }
            .model-test-result { margin-left: 10px; display: inline-block; }
        </style>
        <?php
    }

    public function skip_permission_tests_field() {
        $skip_permission_tests = get_option('document_viewer_skip_permission_tests', false);
        ?>
        <div class="document-viewer-settings-section">
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Directory Permissions', 'document-viewer-plugin'); ?></th>
                    <td>
                        <label for="document_viewer_skip_permission_tests">
                            <input type="checkbox" name="document_viewer_skip_permission_tests"
                                   id="document_viewer_skip_permission_tests"
                                   value="1" <?php checked(1, $skip_permission_tests); ?>>
                            <?php _e('Skip directory permission tests and logging', 'document-viewer-plugin'); ?>
                        </label>
                        <p class="description">
                            <?php _e('Enable this option to skip automatic permission tests and related log entries on directory initialization.', 'document-viewer-plugin'); ?>
                        </p>
                    </td>
                </tr>
            </table>
        </div>

        <h3><?php _e('Login Redirect Settings', 'document-viewer-plugin'); ?></h3>
        <p class="description">
            <?php _e('Configure where users will be redirected after standard WordPress login.', 'document-viewer-plugin'); ?>
        </p>

        <div class="document-viewer-settings-section">
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Global Redirect URL', 'document-viewer-plugin'); ?></th>
                    <td>
                        <input type="url" name="login_global_redirect_url"
                               id="login_global_redirect_url"
                               class="regular-text"
                               value="<?php echo esc_attr(get_option('login_global_redirect_url', '')); ?>"
                               placeholder="<?php echo esc_attr(home_url()); ?>">
                        <p class="description">
                            <?php _e('Enter the URL where users will be redirected after login. Leave empty to use the WordPress default behavior.', 'document-viewer-plugin'); ?>
                        </p>
                    </td>
                </tr>
            </table>
        </div>
        <?php
    }

    public function developer_mode_fields() {
        $developer_mode = get_option('document_viewer_developer_mode', false);
        $developer_response = get_option('document_viewer_developer_response', '');
        ?>
        <div class="document-viewer-settings-section">
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Developer Mode', 'document-viewer-plugin'); ?></th>
                    <td>
                       ```php
                        <label for="document_viewer_developer_mode">
                            <input type="checkbox" name="document_viewer_developer_mode"
                                   id="document_viewer_developer_mode"
                                   value="1" <?php checked(1, $developer_mode); ?>>
                            <?php _e('Enable Developer Mode', 'document-viewer-plugin'); ?>
                        </label>
                        <p class="description">
                            <?php _e('When enabled, API requests will be simulated and the text below will be returned as the response.', 'document-viewer-plugin'); ?>
                        </p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Developer Response', 'document-viewer-plugin'); ?></th>
                    <td>
                        <textarea name="document_viewer_developer_response"
                                  id="document_viewer_developer_response"
                                  rows="8"
                                  style="width: 100%;"><?php echo esc_textarea($developer_response); ?></textarea>
                        <p class="description">
                            <?php _e('This text will be returned as the response when Developer Mode is enabled.', 'document-viewer-plugin'); ?>
                        </p>
                    </td>
                </tr>
            </table>
        </div>
        <?php
    }

    public function sanitize_model_list($input) {
        if (!is_array($input)) {
            return array();
        }

        // Remove empty items and sanitize
        return array_filter(array_map('sanitize_text_field', $input), function($item) {
            return !empty($item) && preg_match('/^[A-Za-z0-9\-_\/\.\:]+$/', $item);
        });
    }

    private function register_document_settings() {
        register_setting('document_viewer_settings', 'document_viewer_max_file_size', array(
            'type' => 'number',
            'sanitize_callback' => function($value) {
                return min(max(intval($value), 1), 100);
            }
        ));

        register_setting('document_viewer_settings', 'document_viewer_allowed_types', array(
            'type' => 'array',
            'sanitize_callback' => function($value) {
                return is_array($value) ? array_map('sanitize_text_field', $value) : array('pdf', 'doc', 'docx', 'txt');
            }
        ));

        register_setting('document_viewer_settings', 'document_viewer_enable_preview', array(
            'type' => 'boolean',
            'sanitize_callback' => 'rest_sanitize_boolean'
         ));

        // Add PhpWord specific settings
        register_setting('document_viewer_settings', 'document_viewer_phpword_consecutive_hyphen_limit', array(
            'type' => 'number',
            'sanitize_callback' => function($value) {
                return min(max(intval($value), 1), 10);
            },
                       'default' => 3
        ));

        register_setting('document_viewer_settings', 'document_viewer_phpword_hyphenation_zone', array(
            'type' => 'number',
            'sanitize_callback' => function($value) {
                return min(max(intval($value), 1), 9999);
            },
                       'default' => 360
        ));

        register_setting('document_viewer_settings', 'document_viewer_phpword_do_not_hyphenate_words_with_numbers', array(
            'type' => 'boolean',
            'sanitize_callback' => 'rest_sanitize_boolean',
            'default' => true
        ));
    }

    private function render_document_api_settings() {
        ?>
        <div class="document-api-section" style="margin-top:20px; background:#fff; padding:25px; border:1px solid #ccc; border-radius:4px;">
            <h3><?php _e('Document Settings', 'document-viewer-plugin'); ?></h3>

            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Max File Size (MB)', 'document-viewer-plugin'); ?></th>
                    <td>
                        <input type="number" name="document_viewer_max_file_size"
                               value="<?php echo esc_attr(get_option('document_viewer_max_file_size', '10')); ?>"
                               min="1" max="100" step="1">
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Allowed File Types', 'document-viewer-plugin'); ?></th>
                    <td>
                        <?php
                        $allowed_types = get_option('document_viewer_allowed_types', array('pdf', 'doc', 'docx', 'txt'));
                        $file_types = array('pdf', 'doc', 'docx', 'txt', 'rtf', 'odt');
                        foreach ($file_types as $type): ?>
                            <label style="margin-right:15px;">
                                <input type="checkbox" name="document_viewer_allowed_types[]"
                                       value="<?php echo esc_attr($type); ?>"
                                       <?php checked(in_array($type, $allowed_types)); ?>>
                                .<?php echo esc_html($type); ?>
                            </label>
                        <?php endforeach; ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Enable File Preview', 'document-viewer-plugin'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="document_viewer_enable_preview"
                                   value="1" <?php checked(get_option('document_viewer_enable_preview', '1')); ?>>
                            <?php _e('Show document preview before upload', 'document-viewer-plugin'); ?>
                        </label>
                    </td>
                </tr>

            </table>

            <h4><?php _e('PhpWord Settings', 'document-viewer-plugin'); ?></h4>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Consecutive Hyphen Limit', 'document-viewer-plugin'); ?></th>
                    <td>
                        <input type="number" name="document_viewer_phpword_consecutive_hyphen_limit"
                               value="<?php echo esc_attr(get_option('document_viewer_phpword_consecutive_hyphen_limit', '3')); ?>"
                               min="1" max="10" step="1">
                        <p class="description"><?php _e('Maximum number of consecutive lines ending with hyphens.', 'document-viewer-plugin'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Hyphenation Zone', 'document-viewer-plugin'); ?></th>
                    <td>
                        <input type="number" name="document_viewer_phpword_hyphenation_zone"
                               value="<?php echo esc_attr(get_option('document_viewer_phpword_hyphenation_zone', '360')); ?>"
                               min="1" max="9999" step="1">
                        <p class="description"><?php _e('Distance in twips from the right margin within which hyphenation is allowed.', 'document-viewer-plugin'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Hyphenation Options', 'document-viewer-plugin'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="document_viewer_phpword_do_not_hyphenate_words_with_numbers"
                                  value="1" <?php checked(1, get_option('document_viewer_phpword_do_not_hyphenate_words_with_numbers', true)); ?>>
                            <?php _e('Do not hyphenate words with numbers', 'document-viewer-plugin'); ?>
                        </label>
                    </td>
                </tr>
            </table>
        </div>
        <?php
    }

    private function get_debug_log_content() {
        // Use plugin-specific log file instead of WordPress main debug log
        $log_file = plugin_dir_path(__FILE__) . 'assets/logs/debug.log';

        // Check if directory exists, if not create it
        $log_dir = dirname($log_file);
        if (!file_exists($log_dir)) {
            wp_mkdir_p($log_dir);
        }

        // Return log content or empty string if file doesn't exist
        if (file_exists($log_file)) {
            return file_get_contents($log_file);
        }
        return '';
    }

    public function test_api_connection() {
        if (!check_ajax_referer('document_viewer_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => __('Security check failed', 'document-viewer-plugin')));
        }

        wp_send_json_success(array('message' => __('Connection successful', 'document-viewer-plugin')));
           return;
    }

    public function test_api_key() {
        if (!check_ajax_referer('document_viewer_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => __('Security check failed', 'document-viewer-plugin')));
        }

        $api_key = isset($_POST['api_key']) ? sanitize_text_field($_POST['api_key']) : '';
        $is_analysis = isset($_POST['is_analysis']) && $_POST['is_analysis'] === 'true';

        if (empty($api_key)) {
            wp_send_json_error(array('message' => __('API key is required', 'document-viewer-plugin')));
            return;
        }

        if ($is_analysis) {
            // Test the Analysis API key by making a request to the status endpoint
            $api_endpoint = get_option('document_viewer_analysis_endpoint', '');

            if (empty($api_endpoint)) {
                wp_send_json_error(array('message' => __('Analysis API endpoint is not configured', 'document-viewer-plugin')));
                return;
            }

            $test_url = rtrim($api_endpoint, '/') . '/status';

            // Log the request attempt for debugging
            error_log('Testing Analysis API key: Making request to ' . $test_url);

            $response = wp_remote_get($test_url, array(
                'headers' => array(
                    'Authorization' => 'Bearer ' . $api_key,
                    'Content-Type' => 'application/json',
                ),
                'timeout' => 15,
                'sslverify' => true,
            ));

            // If there's an error with the request
            if (is_wp_error($response)) {
                error_log('Analysis API test failed: ' . $response->get_error_message());
                wp_send_json_error(array(
                    'message' => __('API Connection Error: ', 'document-viewer-plugin') . $response->get_error_message()
                ));
                return;
            }

            // Get response code
            $status_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);

            // Log the response for debugging
            error_log('Analysis API response code: ' . $status_code);
            error_log('Analysis API response body: ' . substr($body, 0, 200) . '...');

            if ($status_code >= 200 && $status_code < 400) {
                wp_send_json_success(array('message' => __('Analysis API key is valid and working', 'document-viewer-plugin')));
            } else {
                wp_send_json_error(array('message' => sprintf(__('API returned error status: %d', 'document-viewer-plugin'), $status_code)));
            }

            return;
        }

        // For OpenRouter API key testing (unchanged)
        $api_endpoint = get_option('document_viewer_api_endpoint', 'https://openrouter.ai/api/v1');
        $models_endpoint = rtrim($api_endpoint, '/') . '/models';

        // Log the request attempt for debugging
        error_log('Testing OpenRouter API key: Making request to ' . $models_endpoint);

        $response = wp_remote_get($models_endpoint, array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'HTTP-Referer' => site_url(), // OpenRouter requires this header
                'X-Title' => 'Document Viewer Plugin', // Identify your application to OpenRouter
                'Content-Type' => 'application/json',
            ),
            'timeout' => 20, // Increase timeout for slower connections
            'sslverify' => true,
        ));

        // If there's an error with the request
        if (is_wp_error($response)) {
            error_log('OpenRouter API test failed: ' . $response->get_error_message());
            wp_send_json_error(array(
                'message' => __('API Connection Error: ', 'document-viewer-plugin') . $response->get_error_message()
            ));
            return;
        }

        // Get response code and body
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        // Log response
        error_log('OpenRouter API response code: ' . $status_code);
        error_log('OpenRouter API response body: ' . substr($body, 0, 200) . '...');

        $data = json_decode($body, true);

        // Check for error response or invalid status code
        if ($status_code !== 200) {
            $error_message = isset($data['error']) && isset($data['error']['message'])
                ? $data['error']['message']
                : __('API returned status code: ', 'document-viewer-plugin') . $status_code;

            error_log('OpenRouter API error: ' . $error_message);
            wp_send_json_error(array('message' => $error_message));
            return;
        }

        // Success! The API key is valid
        if (isset($data['data']) && is_array($data['data']) && !empty($data['data'])) {
            // Option to automatically populate the model list with available models
            $model_names = array_map(function($model) {
                return isset($model['id']) ? $model['id'] : '';
            }, $data['data']);

            $model_names = array_filter($model_names); // Remove empty values

            wp_send_json_success(array(
                'message' => __('API key is valid. Found ', 'document-viewer-plugin') . count($model_names) . __(' models.', 'document-viewer-plugin'),
                'models' => $model_names
            ));
        } else {
            // API key might be valid but no models were returned
            wp_send_json_success(array(
                'message' => __('API key appears valid, but no models were returned.', 'document-viewer-plugin')
            ));
        }
    }

    public function test_api_endpoint() {
        if (!check_ajax_referer('document_viewer_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => __('Security check failed', 'document-viewer-plugin')));
        }

        $api_endpoint = isset($_POST['api_endpoint']) ? esc_url_raw($_POST['api_endpoint']) : '';
        $is_analysis = isset($_POST['is_analysis']) && $_POST['is_analysis'] === 'true';

        if (empty($api_endpoint)) {
            wp_send_json_error(array('message' => __('API endpoint is required', 'document-viewer-plugin')));
            return;
        }

        // Actually test the endpoint by sending a GET request
        $test_url = rtrim($api_endpoint, '/') . ($is_analysis ? '/status' : '/models');

        $response = wp_remote_get($test_url, array(
            'timeout' => 15,
            'sslverify' => true
        ));

        // If there's an error with the request
        if (is_wp_error($response)) {
            error_log('API endpoint test failed: ' . $response->get_error_message());
            wp_send_json_error(array(
                'message' => __('API Connection Error: ', 'document-viewer-plugin') . $response->get_error_message()
            ));
            return;
        }

        // Get response code
        $status_code = wp_remote_retrieve_response_code($response);

        // Any response that's not a 4xx or 5xx error is considered valid
        if ($status_code >= 200 && $status_code < 400) {
            $message = $is_analysis ?
                __('Analysis API endpoint is valid and reachable', 'document-viewer-plugin') :
                __('API endpoint is valid and reachable', 'document-viewer-plugin');

            wp_send_json_success(array('message' => $message));
        } else {
            wp_send_json_error(array(
                'message' => sprintf(__('API returned error status: %d', 'document-viewer-plugin'), $status_code)
            ));
        }
    }

    public function test_api_model() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        $model = isset($_POST['model']) ? sanitize_text_field($_POST['model']) : '';
        $is_analysis = isset($_POST['is_analysis']) && $_POST['is_analysis'] === 'true';

        if (empty($model)) {
            wp_send_json_error(array('message' => __('Model name is required', 'document-viewer-plugin')));
            return;
        }

        // Get appropriate API configuration based on whether we're testing the analysis API
        $api_key = $is_analysis ?
            get_option('document_viewer_analysis_api_key') :
            get_option('document_viewer_api_key');

        $api_endpoint = $is_analysis ?
            get_option('document_viewer_analysis_endpoint') :
            get_option('document_viewer_api_endpoint', 'https://openrouter.ai/api/v1');

        if (empty($api_key) || empty($api_endpoint)) {
            wp_send_json_error(array('message' => __('API not fully configured. Please check your API settings.', 'document-viewer-plugin')));
            return;
        }

        // For OpenRouter API, check if the model exists in available models
        if (!$is_analysis) {
            $models_endpoint = rtrim($api_endpoint, '/') . '/models';

            $response = wp_remote_get($models_endpoint, array(
                'headers' => array(
                    'Authorization' => 'Bearer ' . $api_key,
                    'HTTP-Referer' => site_url(),
                    'X-Title' => 'Document Viewer Plugin',
                    'Content-Type' => 'application/json',
                ),
                'timeout' => 20,
                'sslverify' => true,
            ));

            if (is_wp_error($response)) {
                wp_send_json_error(array('message' => $response->get_error_message()));
                return;
            }

            $status_code = wp_remote_retrieve_response_code($response);
            if ($status_code !== 200) {
                wp_send_json_error(array('message' => sprintf(__('API returned error status: %d', 'document-viewer-plugin'), $status_code)));
                return;
            }

            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);

            if (!isset($data['data']) || !is_array($data['data'])) {
                wp_send_json_error(array('message' => __('Invalid API response format', 'document-viewer-plugin')));
                return;
            }

            // Check if model exists in available models
            $available_models = array_map(function($model_data) {
                return isset($model_data['id']) ? $model_data['id'] : '';
            }, $data['data']);

            $available_models = array_filter($available_models);

            if (!in_array($model, $available_models)) {
                wp_send_json_error(array('message' => __('Model not found in available models', 'document-viewer-plugin')));
                return;
            }

            wp_send_json_success(array('message' => __('Model is valid and available', 'document-viewer-plugin')));
            return;
        }

        // For Analysis API, just check if the model string is valid
        // Typically we would make a call to verify, but since we don't know the specific API structure
        // we'll just validate the format for now
        if (!preg_match('/^[A-Za-z0-9\-_\/\.]+$/', $model)) {
            wp_send_json_error(array('message' => __('Invalid model name format', 'document-viewer-plugin')));
            return;
        }

        wp_send_json_success(array('message' => __('Analysis model appears valid', 'document-viewer-plugin')));
    }

    public function test_specific_model() {
        // Use wp_verify_nonce directly and let it handle errors automatically
        check_ajax_referer('document_viewer_nonce', 'nonce');

        $model = isset($_POST['model']) ? sanitize_text_field($_POST['model']) : '';

        if (empty($model)) {
            wp_send_json_error(array('message' => __('Model name is required', 'document-viewer-plugin')));
        }

        // Make an actual API call to verify the model exists
        $api_key = get_option('document_viewer_api_key');
        $api_endpoint = get_option('document_viewer_api_endpoint', 'https://openrouter.ai/api/v1');

        if (empty($api_key) || empty($api_endpoint)) {
            wp_send_json_error(array('message' => __('API configuration incomplete', 'document-viewer-plugin')));
            return;
        }

        $models_endpoint = rtrim($api_endpoint, '/') . '/models';

        $response = wp_remote_get($models_endpoint, array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'HTTP-Referer' => site_url(),
                'X-Title' => 'Document Viewer Plugin',
                'Content-Type' => 'application/json',
            ),
            'timeout' => 20,
            'sslverify' => true,
        ));

        if (is_wp_error($response)) {
            wp_send_json_error(array('message' => $response->get_error_message()));
            return;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code !== 200) {
            wp_send_json_error(array('message' => sprintf(__('API returned error status: %d', 'document-viewer-plugin'), $status_code)));
            return;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!isset($data['data']) || !is_array($data['data'])) {
            wp_send_json_error(array('message' => __('Invalid API response format', 'document-viewer-plugin')));
            return;
        }

        // Extract available model IDs
        $available_models = array_map(function($model_data) {
            return isset($model_data['id']) ? $model_data['id'] : '';
        }, $data['data']);

        $available_models = array_filter($available_models);

        // Check if the model is in the list of available models
        if (!in_array($model, $available_models)) {
            wp_send_json_error(array('message' => __('Model not found in available models', 'document-viewer-plugin')));
            return;
        }

        wp_send_json_success(array('message' => __('Model is valid and available', 'document-viewer-plugin')));
    }

    public function quick_save_model() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        $model = isset($_POST['model']) ? sanitize_text_field($_POST['model']) : '';

        if (empty($model)) {
            wp_send_json_error(array('message' => __('Model name is required', 'document-viewer-plugin')));
            return;
        }

        // Validate model name format using the same pattern as in sanitize_model_list()
        if (!preg_match('/^[A-Za-z0-9\-_\/\.]+$/', $model)) {
            wp_send_json_error(array('message' => __('Invalid model name format', 'document-viewer-plugin')));
            return;
        }

        // Check if model exists via API before adding it
        $api_key = get_option('document_viewer_api_key');
        $api_endpoint = get_option('document_viewer_api_endpoint', 'https://openrouter.ai/api/v1');

        if (empty($api_key) || empty($api_endpoint)) {
            wp_send_json_error(array('message' => __('API configuration incomplete', 'document-viewer-plugin')));
            return;
        }

        // Save to model list if validation passes
        $model_list = get_option('document_viewer_model_list', array());
        if (!in_array($model, $model_list)) {
            $model_list[] = $model;
            update_option('document_viewer_model_list', $model_list);
        }

        wp_send_json_success(array('message' => __('Model saved successfully', 'document-viewer-plugin')));
    }

    public function save_api_key() {
        if (!check_ajax_referer('document_viewer_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => __('Security check failed', 'document-viewer-plugin')));
        }

        $api_key = isset($_POST['api_key']) ? sanitize_text_field($_POST['api_key']) : '';

        if (empty($api_key)) {
            wp_send_json_error(array('message' => __('API key is required', 'document-viewer-plugin')));
        }

        update_option('document_viewer_api_key', $api_key);
        wp_send_json_success(array('message' => __('API key saved successfully', 'document-viewer-plugin')));
    }

    public function save_api_endpoint() {
        if (!check_ajax_referer('document_viewer_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => __('Security check failed', 'document-viewer-plugin')));
        }

        $api_endpoint = isset($_POST['api_endpoint']) ? esc_url_raw($_POST['api_endpoint']) : '';

        if (empty($api_endpoint)) {
            wp_send_json_error(array('message' => __('API endpoint is required', 'document-viewer-plugin')));
        }

        update_option('document_viewer_api_endpoint', $api_endpoint);
        wp_send_json_success(array('message' => __('API endpoint saved successfully', 'document-viewer-plugin')));
    }

    public function save_api_model() {
        if (!check_ajax_referer('document_viewer_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => __('Security check failed', 'document-viewer-plugin')));
        }

        $model = isset($_POST['model']) ? sanitize_text_field($_POST['model']) : '';

        if (empty($model)) {
            wp_send_json_error(array('message' => __('Model is required', 'document-viewer-plugin')));
        }

        update_option('document_viewer_model', $model);
        wp_send_json_success(array('message' => __('Model saved successfully', 'document-viewer-plugin')));
    }

    public function save_analysis_api_key() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        $api_key = isset($_POST['api_key']) ? sanitize_text_field($_POST['api_key']) : '';

        if (empty($api_key)) {
            wp_send_json_error(array('message' => __('API key is required', 'document-viewer-plugin')));
            return;
        }

        update_option('document_viewer_analysis_api_key', $api_key);
        wp_send_json_success(array('message' => __('Analysis API key saved successfully', 'document-viewer-plugin')));
    }

    public function save_analysis_api_endpoint() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        $api_endpoint = isset($_POST['api_endpoint']) ? esc_url_raw($_POST['api_endpoint']) : '';

        if (empty($api_endpoint)) {
            wp_send_json_error(array('message' => __('API endpoint is required', 'document-viewer-plugin')));
            return;
        }

        update_option('document_viewer_analysis_endpoint', $api_endpoint);
        wp_send_json_success(array('message' => __('Analysis API endpoint saved successfully', 'document-viewer-plugin')));
    }

    public function save_analysis_model() {
        if (!check_ajax_referer('document_viewer_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => __('Security check failed', 'document-viewer-plugin')));
        }

        $model = isset($_POST['model']) ? sanitize_text_field($_POST['model']) : '';

        update_option('document_viewer_analysis_model', $model);
        wp_send_json_success(array('message' => __('Analysis model saved successfully', 'document-viewer-plugin')));
    }

    public function save_model_list() {
        if (!check_ajax_referer('document_viewer_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => __('Security check failed', 'document-viewer-plugin')));
            return;
        }

        $models = isset($_POST['models']) ? (array) $_POST['models'] : array();

        // Per debug
        dv_debug_log('Saving model list. Raw data: ' . print_r($models, true));

        // Sanitize and validate models
        $sanitized_models = array_filter(array_map(function($model) {
            return sanitize_text_field($model);
        }, $models), function($model) {
            return !empty($model) && preg_match('/^[A-Za-z0-9\-_\/\.\:]+$/', $model);
        });

        dv_debug_log('Sanitized models: ' . print_r($sanitized_models, true));

        // Importante: verifica che l'opzione esista prima di aggiornarla
        $existing_models = get_option('document_viewer_model_list', array());
        dv_debug_log('Existing models before update: ' . print_r($existing_models, true));

        // Aggiorna l'opzione nel database
        $result = update_option('document_viewer_model_list', $sanitized_models);

        // Verifica se l'aggiornamento ha avuto successo
        if ($result) {
            dv_debug_log('Model list saved successfully to database');
            wp_send_json_success(array('message' => __('Model list saved successfully', 'document-viewer-plugin')));
        } else {
            // Se update_option restituisce false, potrebbe essere perché il valore è già lo stesso nel database
            // Facciamo un controllo aggiuntivo
            $current_models = get_option('document_viewer_model_list', array());
            if (count(array_diff($sanitized_models, $current_models)) === 0 &&
                count(array_diff($current_models, $sanitized_models)) === 0) {
                // I modelli sono già aggiornati
                dv_debug_log('Models were already up to date in database');
                wp_send_json_success(array('message' => __('Model list is already up to date', 'document-viewer-plugin')));
            } else {
                dv_debug_log('Failed to save model list to database. Current models: ' . print_r($current_models, true));
                wp_send_json_error(array('message' => __('Failed to save model list to database', 'document-viewer-plugin')));
            }
        }
    }

    public function clear_debug_log() {
        // Use standardized nonce verification
        check_ajax_referer('document_viewer_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
            return;
        }

        $log_file = plugin_dir_path(__FILE__) . 'assets/logs/debug.log';
        $log_dir = dirname($log_file);

        // Make sure log directory exists
        if (!file_exists($log_dir)) {
            if (!wp_mkdir_p($log_dir)) {
                wp_send_json_error(array('message' => __('Could not create log directory', 'document-viewer-plugin')));
                return;
            }
        }

        // Clear the log file using direct file operations
        // This is safe because we're only targeting a specific log file in the plugin directory
        if (file_put_contents($log_file, '') !== false) {
            wp_send_json_success(array('message' => __('Log cleared successfully', 'document-viewer-plugin')));
        } else {
            wp_send_json_error(array('message' => __('Failed to clear log. Check file permissions.', 'document-viewer-plugin')));
        }
    }

    public function refresh_debug_log() {
        if (!check_ajax_referer('document_viewer_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => __('Security check failed', 'document-viewer-plugin')));
        }

        wp_send_json_success(array('content' => $this->get_debug_log_content()));
    }

    public function settings_chat_model() {
        if (!check_ajax_referer('document_viewer_nonce', 'nonce', false)) {
            wp_send_json_error(array('message' => __('Security check failed', 'document-viewer-plugin')));
        }

        $message = isset($_POST['chat_message']) ? sanitize_textarea_field($_POST['chat_message']) : '';

        if (empty($message)) {
            wp_send_json_error(array('message' => __('Message is required', 'document-viewer-plugin')));
        }

        // For simple "ping" tests, we don't need to make a real API call
        if (strtolower(trim($message)) === 'ping') {
            wp_send_json_success(array('reply' => 'Pong! Connection is working.'));
            return;
        }

        // For real messages, call the OpenRouter API
        $api_key = get_option('document_viewer_api_key');
        $api_endpoint = get_option('document_viewer_api_endpoint');
        $api_model = get_option('document_viewer_model');

        // Ensure the endpoint always points to the chat completions endpoint
        $api_url = rtrim($api_endpoint, '/') . '/chat/completions';

        if (empty($api_key) || empty($api_endpoint) || empty($api_model)) {
            wp_send_json_error(array('message' => __('API not fully configured. Please check your API settings.', 'document-viewer-plugin')));
            return;
        }

        // Setup request
        $headers = array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $api_key,
            'HTTP-Referer' => site_url(), // Required by OpenRouter
            'X-Title' => 'Document Viewer Plugin', // Identify app to OpenRouter
            'Accept' => 'application/json'
        );

        // Structure the request body according to OpenRouter's expectations
        $request_body = array(
            'model' => $api_model,
            'messages' => array(
                array(
                    'role' => 'system',
                    'content' => 'You are a helpful assistant.'
                ),
                array(
                    'role' => 'user',
                    'content' => $message
                )
            ),
            'temperature' => 0.7,
            'max_tokens' => 500
        );

        $request_args = array(
            'headers' => $headers,
            'body' => wp_json_encode($request_body),
            'method' => 'POST',
            'timeout' => 60,
            'data_format' => 'body',
            'sslverify' => true,
            'redirection' => 5,
            'httpversion' => '1.1',
            'blocking' => true,
            'cookies' => array()
        );

        // Log request details
        error_log('Settings Chat API Request - URL: ' . $api_url);
        error_log('Settings Chat API Request - Model: ' . $api_model);

        // Make request
        $response = wp_remote_post($api_url, $request_args);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            error_log('Settings Chat API Error: ' . $error_message);
            wp_send_json_error(array('message' => __('Connection failed: ', 'document-viewer-plugin') . $error_message));
            return;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        // Log response
        error_log('Settings Chat API Response Code: ' . $response_code);

        if ($response_code !== 200) {
            wp_send_json_error(array('message' => __('API returned error code: ', 'document-viewer-plugin') . $response_code));
            return;
        }

        $response_data = json_decode($response_body, true);

        // Error handling for API response
        if (empty($response_data)) {
            wp_send_json_error(array('message' => __('Invalid API response format - empty response', 'document-viewer-plugin')));
            return;
        }

        if (!isset($response_data['choices']) || empty($response_data['choices'])) {
            wp_send_json_error(array('message' => __('Invalid API response format - no choices returned', 'document-viewer-plugin')));
            return;
        }

        if (!isset($response_data['choices'][0]['message']['content'])) {
            wp_send_json_error(array('message' => __('Invalid API response format - no content in response', 'document-viewer-plugin')));
            return;
        }

        // Return the model's reply
        $reply = $response_data['choices'][0]['message']['content'];
        wp_send_json_success(array('reply' => $reply));
    }

    public function execute_composer() {
        check_ajax_referer('document_viewer_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied', 'document-viewer-plugin')));
            return;
        }

        $command = isset($_POST['command']) ? sanitize_text_field($_POST['command']) : '';

        if (!in_array($command, array('install', 'update'))) {
            wp_send_json_error(array('message' => __('Invalid command', 'document-viewer-plugin')));
            return;
        }

        $plugin_dir = plugin_dir_path(__FILE__);

        // Use WP_Filesystem instead of direct shell_exec for better security
        global $wp_filesystem;
        if (!function_exists('WP_Filesystem')) {
            require_once ABSPATH . 'wp-admin/includes/file.php';
        }

        WP_Filesystem();

        // If we can't access the filesystem directly, use WP_CLI if available
        if (defined('WP_CLI') && WP_CLI) {
            $output = array();
            $return_var = 0;
            exec('cd ' . escapeshellarg($plugin_dir) . ' && composer ' . escapeshellarg($command) . ' 2>&1', $output, $return_var);
            $output = implode("\n", $output);
        } else {
            // Fallback to a PHP subprocess with restricted composer commands
            $output = "Using PHP subprocess for composer...\n";

            // Create composer command file
            $temp_script = $plugin_dir . 'composer-runner.php';
            $script_content = '<?php
            chdir("' . addslashes($plugin_dir) . '");
            echo "Running composer ' . $command . '...\n";
            passthru("composer ' . $command . ' 2>&1");
            echo "\nComposer operation completed.";';

            // Write and execute the script
            $wp_filesystem->put_contents($temp_script, $script_content);

            // Execute using PHP directly, not shell
            $output .= shell_exec(PHP_BINARY . ' ' . escapeshellarg($temp_script));

            // Clean up
            $wp_filesystem->delete($temp_script);
        }

        wp_send_json_success(array('output' => $output ?: __('Command executed with no output', 'document-viewer-plugin')));
    }

    /**
     * Apply PhpWord settings to a PHPWord document instance
     *
     * @param \PhpOffice\PhpWord\PhpWord $phpWord The PHPWord instance
     * @return \PhpOffice\PhpWord\PhpWord The PHPWord instance with settings applied
     */
    public function apply_phpword_settings($phpWord) {
        // Get settings from options
        $consecutiveHyphenLimit = get_option('document_viewer_phpword_consecutive_hyphen_limit', 3);
        $hyphenationZone = get_option('document_viewer_phpword_hyphenation_zone', 360);
        $doNotHyphenateWordsWithNumbers = get_option('document_viewer_phpword_do_not_hyphenate_words_with_numbers', true);

        // Apply settings to PhpWord settings object
        $settings = $phpWord->getSettings();

        // Apply hyphenation settings
        if (method_exists($settings, 'setConsecutiveHyphenLimit')) {
            $settings->setConsecutiveHyphenLimit($consecutiveHyphenLimit);
        }

        if (method_exists($settings, 'setHyphenationZone')) {
            $settings->setHyphenationZone($hyphenationZone);
        }

        if (method_exists($settings, 'setDoNotHyphenateCaps')) {
            $settings->setDoNotHyphenateCaps(true);
        }

        // Set tracking changes
        if (method_exists($settings, 'setTrackRevisions')) {
            $settings->setTrackRevisions(false);
        }

        return $phpWord;
    }

    public function add_preset_query() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'document-viewer-plugin')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action.', 'document-viewer-plugin')]);
        }

        // Validate inputs
        $title = isset($_POST['title']) ? sanitize_text_field($_POST['title']) : '';
        $query_text = isset($_POST['query_text']) ? wp_kses_post($_POST['query_text']) : '';

        if (empty($title) || empty($query_text)) {
            wp_send_json_error(['message' => __('Title and query text are required.', 'document-viewer-plugin')]);
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'document_preset_queries';

        // Insert query
        $result = $wpdb->insert(
            $table_name,
            [
                'title' => $title,
                'query_text' => $query_text,
                'created_at' => current_time('mysql')
            ],
            ['%s', '%s', '%s']
        );

        if ($result === false) {
            wp_send_json_error(['message' => __('Failed to add preset query. Database error.', 'document-viewer-plugin')]);
        }

        $query_id = $wpdb->insert_id;

        wp_send_json_success([
            'message' => __('Preset query added successfully.', 'document-viewer-plugin'),
            'query_id' => $query_id
        ]);
    }

    /**
     * Update a preset query
     */
    public function update_preset_query() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'document-viewer-plugin')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action.', 'document-viewer-plugin')]);
        }

        // Validate inputs
        $query_id = isset($_POST['query_id']) ? intval($_POST['query_id']) : 0;
        $title = isset($_POST['title']) ? sanitize_text_field($_POST['title']) : '';
        $query_text = isset($_POST['query_text']) ? wp_kses_post($_POST['query_text']) : '';

        if (empty($query_id) || empty($title) || empty($query_text)) {
            wp_send_json_error(['message' => __('Query ID, title, and query text are required.', 'document-viewer-plugin')]);
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'document_preset_queries';

        // Update query
        $result = $wpdb->update(
            $table_name,
            [
                'title' => $title,
                'query_text' => $query_text,
                'updated_at' => current_time('mysql')
            ],
            ['id' => $query_id],
            ['%s', '%s', '%s'],
            ['%d']
        );

        if ($result === false) {
            wp_send_json_error(['message' => __('Failed to update preset query. Database error.', 'document-viewer-plugin')]);
        }

        wp_send_json_success(['message' => __('Preset query updated successfully.', 'document-viewer-plugin')]);
    }

    /**
     * Delete a preset query
     */
    public function delete_preset_query() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'document-viewer-plugin')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action.', 'document-viewer-plugin')]);
        }

        // Validate inputs
        $query_id = isset($_POST['query_id']) ? intval($_POST['query_id']) : 0;

        if (empty($query_id)) {
            wp_send_json_error(['message' => __('Query ID is required.', 'document-viewer-plugin')]);
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'document_preset_queries';

        // Delete query
        $result = $wpdb->delete(
            $table_name,
            ['id' => $query_id],
            ['%d']
        );

        if ($result === false) {
            wp_send_json_error(['message' => __('Failed to delete preset query. Database error.', 'document-viewer-plugin')]);
        }

        wp_send_json_success(['message' => __('Preset query deleted successfully.', 'document-viewer-plugin')]);
    }

    /**
     * Add a set of predefined financial queries
     */
    public function add_financial_query_set() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'document_viewer_nonce')) {
            wp_send_json_error(['message' => __('Security check failed.', 'document-viewer-plugin')]);
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('You do not have permission to perform this action.', 'document-viewer-plugin')]);
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'document_preset_queries';
        $current_time = current_time('mysql');

        // Financial queries to add
        $financial_queries = [
            [
                'title' => __('Analisi del coefficiente di indebitamento', 'document-viewer-plugin'),
                'query_text' => __('Analizza il coefficiente di indebitamento di questa azienda confrontandolo con la media del settore. Valuta se il livello attuale è sostenibile a lungo termine.', 'document-viewer-plugin')
            ],
            [
                'title' => __('ROI e ROE assessment', 'document-viewer-plugin'),
                'query_text' => __('Analizza il Return on Investment (ROI) e il Return on Equity (ROE) in questo documento, confrontandoli con i benchmark del settore e commentando la loro sostenibilità.', 'document-viewer-plugin')
            ],
            [
                'title' => __('Liquidità e solvibilità', 'document-viewer-plugin'),
                'query_text' => __('Valuta i rapporti di liquidità corrente e immediata. L\'azienda è in grado di far fronte alle proprie obbligazioni a breve termine? Quali sono i rischi di liquidità?', 'document-viewer-plugin')
            ],
            [
                'title' => __('Analisi del ciclo di conversione del denaro', 'document-viewer-plugin'),
                'query_text' => __('Calcola e valuta il ciclo di conversione del denaro (CCC) dell\'azienda. Come si confronta con il settore? Quali miglioramenti sono possibili nella gestione del capitale circolante?', 'document-viewer-plugin')
            ],
            // Aggiungi nuove query finanziarie specializzate
            [
                'title' => __('Analisi del valore attuale netto (VAN)', 'document-viewer-plugin'),
                'query_text' => __('Analizza i calcoli del valore attuale netto (VAN) presenti nel documento. La metodologia utilizzata è corretta? Il tasso di sconto è appropriato per il profilo di rischio dell\'investimento? Quali sono le implicazioni per le decisioni di investimento?', 'document-viewer-plugin')
            ],
            [
                'title' => __('Analisi della sostenibilità del debito', 'document-viewer-plugin'),
                'query_text' => __('Valuta la sostenibilità del debito a lungo termine dell\'azienda. Analizza il rapporto debito/EBITDA, il grado di copertura degli interessi e la struttura delle scadenze del debito. L\'azienda ha una strategia chiara per la gestione del debito?', 'document-viewer-plugin')
            ],
            [
                'title' => __('Qualità degli utili', 'document-viewer-plugin'),
                'query_text' => __('Valuta la qualità degli utili riportati. Vi sono elementi straordinari o una tantum che distorcono i risultati? La conversione degli utili in flussi di cassa è efficiente? Identifica eventuali segnali di allerta o pratiche contabili aggressive.', 'document-viewer-plugin')
            ],
            [
                'title' => __('Analisi del free cash flow', 'document-viewer-plugin'),
                'query_text' => __('Analizza il free cash flow dell\'azienda negli ultimi periodi. Come viene allocato il capitale (dividendi, riacquisto di azioni, investimenti, riduzione del debito)? La strategia di allocazione del capitale è coerente con gli obiettivi di lungo termine?', 'document-viewer-plugin')
            ],
            [
                'title' => __('Margini di profitto e trend', 'document-viewer-plugin'),
                'query_text' => __('Esamina i margini di profitto lordo, operativo e netto. Quali sono i trend negli ultimi periodi? Come si confrontano con i competitor? Quali sono i fattori principali che influenzano questi margini?', 'document-viewer-plugin')
            ],
            [
                'title' => __('Analisi DuPont del ROE', 'document-viewer-plugin'),
                'query_text' => __('Esegui una scomposizione DuPont del ROE per identificare i driver della redditività (margine di profitto, rotazione dell\'attivo, leva finanziaria). Quali componenti hanno maggiore impatto sulla performance finanziaria dell\'azienda?', 'document-viewer-plugin')
            ],
            [
                'title' => __('EBITDA e aggiustamenti', 'document-viewer-plugin'),
                'query_text' => __('Analizza l\'EBITDA e l\'EBITDA rettificato presentati nel documento. Gli aggiustamenti sono ragionevoli e ben documentati? Vi sono elementi ricorrenti che vengono trattati come straordinari? Calcola il rapporto EBITDA/Interessi passivi.', 'document-viewer-plugin')
            ],
            [
                'title' => __('Efficienza operativa', 'document-viewer-plugin'),
                'query_text' => __('Valuta l\'efficienza operativa dell\'azienda analizzando indicatori chiave come la rotazione del magazzino, la rotazione dei crediti e il ciclo operativo. Identifica aree di miglioramento e confronta con i benchmark del settore.', 'document-viewer-plugin')
            ],
            [
                'title' => __('Analisi di sensitività', 'document-viewer-plugin'),
                'query_text' => __('Esamina le analisi di sensitività presenti nel documento. I parametri considerati sono appropriati? Come cambia la valutazione dell\'azienda o del progetto al variare delle ipotesi chiave? Quali sono le variabili con maggiore impatto sui risultati?', 'document-viewer-plugin')
            ],
            [
                'title' => __('Costo del capitale e WACC', 'document-viewer-plugin'),
                'query_text' => __('Analizza il calcolo del costo medio ponderato del capitale (WACC). Le componenti di costo del debito e del capitale proprio sono appropriate? La struttura del capitale utilizzata riflette l\'obiettivo di lungo termine dell\'azienda?', 'document-viewer-plugin')
            ],
            [
                'title' => __('Analisi delle previsioni finanziarie', 'document-viewer-plugin'),
                'query_text' => __('Valuta la qualità e la ragionevolezza delle previsioni finanziarie contenute nel documento. Le assunzioni di crescita sono realistiche rispetto alle performance storiche e alle condizioni di mercato? Identifica potenziali incongruenze o rischi nelle proiezioni.', 'document-viewer-plugin')
            ]
        ];

        // Count successful insertions
        $success_count = 0;

        // Insert queries
        foreach ($financial_queries as $query) {
            $result = $wpdb->insert(
                $table_name,
                [
                    'title' => $query['title'],
                    'query_text' => $query['query_text'],
                    'created_at' => $current_time
                ],
                ['%s', '%s', '%s']
            );

            if ($result !== false) {
                $success_count++;
            }
        }

        // Check if any queries were added
        if ($success_count === 0) {
            wp_send_json_error(['message' => __('Failed to add financial queries. Database error.', 'document-viewer-plugin')]);
        }

        wp_send_json_success([
            'message' => sprintf(
                __('%d financial queries added successfully.', 'document-viewer-plugin'),
                $success_count
            )
        ]);
    }

    public function help_line_content_field() {
        // Get the current help line content (or default if it doesn't exist)
        $help_line_content = get_option('help_line_content');

        if (empty($help_line_content)) {
            // If no content exists, load the default content from the Help_Line class
            if (class_exists('Help_Line')) {
                $help_line = new Help_Line();
                $help_line_content = $help_line->get_default_content();
            }
        }

        // Check if the WordPress visual editor is available
        if (function_exists('wp_editor')) {
            $editor_settings = array(
                'textarea_name' => 'help_line_content',
                'textarea_rows' => 10,
                'media_buttons' => false,
                'teeny'         => true,
                'quicktags'     => array('buttons' => 'strong,em,link,ul,ol,li,code'),
                'tinymce'       => array(
                    'plugins'                       => 'lists,paste,tabfocus,wplink,wordpress',
                    'paste_as_text'                 => true,
                    'paste_auto_cleanup_on_paste'   => true,
                    'paste_remove_spans'            => true,
                    'paste_remove_styles'           => true,
                    'paste_remove_styles_if_webkit' => true,
                    'paste_strip_class_attributes'  => true,
                    'toolbar1'                      => 'formatselect,bold,italic,bullist,numlist,link,unlink',
                    'toolbar2'                      => '',
                    'content_css'                   => plugin_dir_url(dirname(__FILE__)) . 'assets/css/help-line-editor.css'
                )
            );

            // Output the WordPress editor
            wp_editor($help_line_content, 'help_line_content_editor', $editor_settings);
            ?>
            <p class="description">
                <?php _e('Questo contenuto verrà visualizzato nella Help Line presente nelle pagine di analisi dei documenti. Puoi utilizzare HTML di base come elenchi, link e formattazione del testo.', 'document-viewer-plugin'); ?>
            </p>

            <div class="help-line-preview" style="margin-top: 20px;">
                <h4><?php _e('Anteprima:', 'document-viewer-plugin'); ?></h4>
                <div class="help-line-preview-content" style="border: 1px solid #ddd; padding: 15px; background: #f9f9f9; max-height: 500px; overflow-y: auto;">
                    <?php echo wp_kses_post($help_line_content); ?>
                </div>
            </div>
            <?php
        } else {
            // Fallback to a simple textarea if wp_editor is not available
            ?>
            <textarea name="help_line_content" id="help_line_content" rows="100" cols="50" class="large-text"><?php echo esc_textarea($help_line_content); ?></textarea>
            <p class="description">
                <?php _e('Questo contenuto verrà visualizzato nella Help Line presente nelle pagine di analisi dei documenti. Puoi utilizzare HTML di base come elenchi, link e formattazione del testo.', 'document-viewer-plugin'); ?>
            </p>
            <?php
        }    }

    /**
     * Get default Office Add-in HTML content
     *
     * @return string Default HTML content for the Office Add-in
     */
    public function get_default_office_addin_content() {
        return '
<div class="excel-addin-container">
    <h2>Financial Advisor Excel Add-in</h2>

    <!-- Excel-like Grid Section (Preview Mode Only) -->
    <div class="section" id="excel-grid-section" style="display: none;">
        <h3>Excel Data Grid (Preview Mode)</h3>
        <p>This grid is only available in preview mode. In the actual Excel add-in, data is extracted directly from Excel cells.</p>
        <div class="excel-grid-placeholder">
            <p><em>Excel-like grid will be displayed here in preview mode.</em></p>
        </div>
    </div>

    <div class="section">
        <h3>1. Extract Text from Excel</h3>
        <p>Select cells in your Excel sheet and click the button below to extract text for analysis.</p>
        <button id="extract-text" class="primary-button" title="Extract text from selected cells in Excel">
            <span class="button-icon">📋</span> Extract Selected Cells
        </button>
        <div id="extracted-text-container" style="display:none;">
            <h4>Extracted Text:</h4>
            <div id="extracted-text" class="text-display" style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 8px; margin-top: 10px; background: #f9f9f9;"></div>
        </div>
    </div>

    <div class="section">
        <h3>2. Analysis Options</h3>

        <div class="form-group">
            <label for="predefined-query">Select a predefined query:</label>
            <select id="predefined-query" class="form-control">
                <option value="">Loading queries...</option>
            </select>
        </div>

        <div class="form-group">
            <label for="custom-query">Or enter a custom question:</label>
            <input type="text" id="custom-query" class="form-control" placeholder="E.g., What are the key financial trends?">
        </div>

        <button id="analyze-button" class="primary-button" title="Analyze the extracted data">
            <span class="button-icon">🔍</span> Analyze Data
        </button>
    </div>

    <div class="section">
        <h3>3. Analysis Results</h3>
        <div id="analysis-results">
            <p class="placeholder">Results will appear here after analysis.</p>
        </div>
    </div>

    <div class="section">
        <h3>4. API Connection Status</h3>
        <div class="status-info">
            <p><strong>Status:</strong> <span id="api-status">Checking...</span></p>
            <p><strong>Model:</strong> <span id="selected-model">Loading...</span></p>
        </div>
    </div>
</div>';
    }

    /**
     * Get default Office Add-in CSS content
     *
     * @return string Default CSS content for the Office Add-in
     */
    public function get_default_office_addin_css() {
        return '/* CSS standard per Excel add-in - Non modificare */
/* Questi stili sono necessari per il corretto funzionamento dell\'add-in */
.excel-addin-container {
    font-family: "Segoe UI", Arial, sans-serif;
    padding: 10px;
    width: 320px;
    margin: 0 auto;
    box-sizing: border-box;
}
.section {
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f9f9f9;
}
h2 {
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    font-size: 18px;
}
h3 {
    color: #3498db;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
}
h4 {
    color: #34495e;
    margin-top: 10px;
    margin-bottom: 8px;
    font-size: 14px;
}
.form-group {
    margin-bottom: 10px;
}
label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    font-size: 12px;
}
.form-control {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ccc;
    border-radius: 3px;
    font-size: 12px;
    box-sizing: border-box;
}
.primary-button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    font-size: 12px;
    width: 100%;
    margin-bottom: 5px;
}
.primary-button:hover {
    background-color: #2980b9;
}
.secondary-button {
    background-color: #95a5a6;
    color: white;
    border: none;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
}
.secondary-button:hover {
    background-color: #7f8c8d;
}
.button-icon {
    margin-right: 5px;
}
.text-display {
    font-family: "Consolas", "Monaco", monospace;
    font-size: 11px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
}
.placeholder {
    color: #7f8c8d;
    font-style: italic;
    font-size: 11px;
}
.status-info p {
    margin: 5px 0;
    font-size: 11px;
}
#api-status, #selected-model {
    font-weight: bold;
}
.excel-grid-placeholder {
    padding: 20px;
    text-align: center;
    background: #f8f9fa;
    border: 1px dashed #dee2e6;
    border-radius: 4px;
    color: #6c757d;
}

/* Excel-like Grid Styles for Preview Mode */
.excel-grid-container {
    margin: 15px 0;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    background: #fff;
}

.excel-grid-header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #d0d0d0;
    font-weight: 600;
    color: #333;
    font-size: 12px;
}

.excel-grid-controls {
    padding: 8px 12px;
    background: #f8f9fa;
    border-bottom: 1px solid #d0d0d0;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.grid-button {
    background: #0078d4;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 10px;
    transition: background-color 0.2s;
}

.grid-button:hover {
    background: #106ebe;
}

.excel-grid-wrapper {
    max-height: 300px;
    overflow: auto;
    border-bottom: 1px solid #d0d0d0;
}

.excel-grid {
    width: 100%;
    border-collapse: collapse;
    font-family: \'Segoe UI\', Arial, sans-serif;
    font-size: 10px;
}

.excel-grid th,
.excel-grid td {
    border: 1px solid #d0d0d0;
    padding: 0;
    margin: 0;
    text-align: center;
    vertical-align: middle;
}

.excel-grid th.row-header,
.excel-grid td.row-header {
    background: #f8f9fa;
    color: #666;
    font-weight: 600;
    width: 30px;
    min-width: 30px;
    max-width: 30px;
    font-size: 9px;
    user-select: none;
}

.excel-grid th.col-header {
    background: #f8f9fa;
    color: #666;
    font-weight: 600;
    height: 20px;
    font-size: 9px;
    user-select: none;
    width: 60px;
    min-width: 60px;
}

.excel-grid .grid-cell {
    padding: 0;
    position: relative;
    width: 60px;
    min-width: 60px;
    height: 18px;
}

.excel-grid .grid-cell.selected {
    background: #cce7ff !important;
    border: 2px solid #0078d4 !important;
}

.excel-grid .cell-input {
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    padding: 1px 3px;
    font-family: inherit;
    font-size: inherit;
    background: transparent;
    box-sizing: border-box;
}

.excel-grid .cell-input:focus {
    background: #fff;
    border: 1px solid #0078d4;
    z-index: 10;
    position: relative;
}

.grid-selection-info {
    padding: 6px 12px;
    background: #f8f9fa;
    font-size: 9px;
    color: #666;
    border-top: 1px solid #e0e0e0;
}';
    }
}
new Document_Viewer_Settings();


